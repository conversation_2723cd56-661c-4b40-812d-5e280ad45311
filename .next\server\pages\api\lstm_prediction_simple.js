"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_prediction_simple";
exports.ids = ["pages/api/lstm_prediction_simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_prediction_simple.ts */ \"(api)/./src/pages/api/lstm_prediction_simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_prediction_simple\",\n        pathname: \"/api/lstm_prediction_simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_prediction_simple.ts":
/*!*************************************************!*\
  !*** ./src/pages/api/lstm_prediction_simple.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Per-symbol mutex using in-memory Map with timestamps\nconst processing = new Map();\n// Circuit breaker pattern - track failures per symbol\nconst failureCount = new Map();\nconst lastFailureTime = new Map();\nconst FAILURE_THRESHOLD = 3;\nconst CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes\n// Cleanup stale processing entries (older than 5 minutes)\nconst cleanupStaleProcessing = ()=>{\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    for (const [symbol, info] of processing.entries()){\n        if (info.active && now - info.startTime > fiveMinutes) {\n            console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);\n            processing.delete(symbol);\n        }\n    }\n};\n// Check if circuit breaker is open for a symbol\nconst isCircuitBreakerOpen = (symbol)=>{\n    const failures = failureCount.get(symbol) || 0;\n    const lastFailure = lastFailureTime.get(symbol) || 0;\n    const now = Date.now();\n    if (failures >= FAILURE_THRESHOLD) {\n        if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {\n            return true; // Circuit breaker is open\n        } else {\n            // Reset circuit breaker after timeout\n            failureCount.delete(symbol);\n            lastFailureTime.delete(symbol);\n            return false;\n        }\n    }\n    return false;\n};\n// Record a failure for circuit breaker\nconst recordFailure = (symbol)=>{\n    const failures = (failureCount.get(symbol) || 0) + 1;\n    failureCount.set(symbol, failures);\n    lastFailureTime.set(symbol, Date.now());\n    console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);\n};\n// Run cleanup every minute\nsetInterval(cleanupStaleProcessing, 60 * 1000);\n// Korean summary mapping\nconst getKoreanSummary = (predictions)=>{\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    if (correctCount === 2) {\n        return \"모두 예측 성공 결과 : green\";\n    } else if (correctCount === 1) {\n        return \"2일 예측 중 1일 예측 실패 결과 : yellow\";\n    } else {\n        return \"모두 예측 실패 결과 : red\";\n    }\n};\n// Traffic light color determination\nconst getTrafficLightColor = (result, processType)=>{\n    if (processType === 'LSTM') {\n        if (result.predictions && Array.isArray(result.predictions)) {\n            const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            if (correctCount === 2) return 'green';\n            if (correctCount === 1) return 'yellow';\n            return 'red';\n        }\n        // Fallback to result_color if available\n        return result.result_color || result.color || 'yellow';\n    } else if (processType === 'MFI') {\n        return result.traffic_light || result.color || 'yellow';\n    }\n    return 'yellow';\n};\n// Calculate LSTM accuracy percentage\nconst calculateLSTMAccuracy = (predictions)=>{\n    if (!Array.isArray(predictions) || predictions.length === 0) return 0;\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    return Math.round(correctCount / predictions.length * 100);\n};\n// Get LSTM pred_prob_up value\nconst getLSTMPredProbUp = (result)=>{\n    if (result.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {\n        return result.predictions[0].pred_prob_up || 0;\n    }\n    return result.pred_prob_up || 0;\n};\n// Get MFI numerical value\nconst getMFIValue = (result)=>{\n    return result.mfi_14 || result.mfi || 0;\n};\n// Get service-specific values for logging and fine-tuning\nconst getBollingerValue = (result)=>{\n    return result.percent_b || result.bollinger_position || result.position || 0;\n};\nconst getRSIValue = (result)=>{\n    return result.rsi_14 || result.rsi || 0;\n};\nconst getIndustryValue = (result)=>{\n    return result.industry || result.industry_sentiment || result.sentiment || 'neutral';\n};\nconst getGARCHValue = (result)=>{\n    return result.var_pct || result.volatility_forecast || result.volatility || 0;\n};\n// Get traffic light color from service results\nconst getServiceTrafficLight = (result)=>{\n    return result.traffic_light || result.color || 'yellow';\n};\n// Majority vote logic for technical analysis (MFI, Bollinger, RSI)\nconst getTechnicalMajorityVote = (mfiColor, bollingerColor, rsiColor)=>{\n    const colors = [\n        mfiColor,\n        bollingerColor,\n        rsiColor\n    ];\n    const colorCounts = {\n        green: colors.filter((c)=>c === 'green').length,\n        yellow: colors.filter((c)=>c === 'yellow').length,\n        red: colors.filter((c)=>c === 'red').length\n    };\n    // Return the color with the highest count\n    if (colorCounts.green >= 2) return 'green';\n    if (colorCounts.red >= 2) return 'red';\n    return 'yellow'; // Default to yellow if no majority or all different\n};\n// Save fine-tuning data with enhanced LSTM and all service results\nconst saveFinetuningData = async (ticker, lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, garchResult)=>{\n    try {\n        const finetuningDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'finetuning');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(finetuningDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(finetuningDir, {\n                recursive: true\n            });\n        }\n        const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\\..+/, '').replace('T', '_');\n        const filename = `${ticker}_${timestamp}.json`;\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(finetuningDir, filename);\n        // Extract LSTM metrics including pred_prob_up\n        const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;\n        const lstmPredProbUp = lstmResult ? getLSTMPredProbUp(lstmResult) : 0;\n        const lstmColor = lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red';\n        // Extract service metrics\n        const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;\n        const mfiColor = mfiResult ? getTrafficLightColor(mfiResult, 'MFI') : 'red';\n        const bollingerValue = bollingerResult ? getBollingerValue(bollingerResult) : 0;\n        const bollingerColor = bollingerResult ? getServiceTrafficLight(bollingerResult) : 'red';\n        const rsiValue = rsiResult ? getRSIValue(rsiResult) : 0;\n        const rsiColor = rsiResult ? getServiceTrafficLight(rsiResult) : 'red';\n        const industryValue = industryResult ? getIndustryValue(industryResult) : 'unknown';\n        const industryColor = industryResult ? getServiceTrafficLight(industryResult) : 'red';\n        const garchValue = garchResult ? getGARCHValue(garchResult) : 0;\n        const garchColor = garchResult ? getServiceTrafficLight(garchResult) : 'red';\n        // Calculate technical majority vote\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        const finetuningData = {\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"\"\n                },\n                {\n                    role: \"user\",\n                    content: `[LSTM accuracy: ${lstmAccuracy}%, pred_prob_up: ${lstmPredProbUp}, Traffic light: ${lstmColor.toUpperCase()}] [Technical (MFI: ${mfiValue}, Bollinger: ${bollingerValue}, RSI: ${rsiValue}), Traffic light: ${technicalColor.toUpperCase()}] [Sentiment (Industry: ${industryValue}), Traffic light: ${industryColor.toUpperCase()}] [Risk (GARCH: ${garchValue}), Traffic light: ${garchColor.toUpperCase()}]`\n                },\n                {\n                    role: \"assistant\",\n                    content: \"\"\n                }\n            ]\n        };\n        fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');\n        console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);\n    } catch (error) {\n        console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n    }\n};\n// Execute a process and return its result with enhanced logging\nconst executeProcess = (scriptPath, ticker, processName)=>{\n    return new Promise((resolve, reject)=>{\n        const servicesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services');\n        const startTime = Date.now();\n        console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);\n        const childProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            scriptPath,\n            ticker\n        ], {\n            cwd: servicesDir,\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ],\n            env: {\n                ...process.env,\n                PYTHONUNBUFFERED: '1'\n            }\n        });\n        let stdout = '';\n        let stderr = '';\n        childProcess.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        childProcess.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        childProcess.on('close', (code)=>{\n            const executionTime = Date.now() - startTime;\n            console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);\n            if (code === 0) {\n                try {\n                    // Parse the JSON output from the last line\n                    const lines = stdout.trim().split('\\n').filter((line)=>line.trim());\n                    // Find the line that looks like JSON (starts with '{')\n                    let jsonLine = null;\n                    for(let i = lines.length - 1; i >= 0; i--){\n                        const line = lines[i].trim();\n                        if (line.startsWith('{')) {\n                            jsonLine = line;\n                            break;\n                        }\n                    }\n                    if (!jsonLine) {\n                        throw new Error(`No JSON output found in ${processName} stdout`);\n                    }\n                    const jsonOutput = JSON.parse(jsonLine);\n                    // Enhanced result logging\n                    if (processName === 'LSTM') {\n                        const accuracy = calculateLSTMAccuracy(jsonOutput.predictions || []);\n                        const predProbUp = getLSTMPredProbUp(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');\n                        console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, pred_prob_up: ${predProbUp}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'MFI') {\n                        const mfiValue = getMFIValue(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');\n                        console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'BOLLINGER') {\n                        const bollingerValue = getBollingerValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[BOLLINGER_RESULT] ${ticker} - Percent B: ${bollingerValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'RSI') {\n                        const rsiValue = getRSIValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[RSI_RESULT] ${ticker} - RSI Value: ${rsiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'INDUSTRY') {\n                        const industryValue = getIndustryValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[INDUSTRY_RESULT] ${ticker} - Industry: ${industryValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'GARCH') {\n                        const garchValue = getGARCHValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[GARCH_RESULT] ${ticker} - VaR %: ${garchValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    }\n                    resolve(jsonOutput);\n                } catch (parseError) {\n                    console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);\n                    reject(new Error(`Failed to parse ${processName} output: ${parseError}`));\n                }\n            } else {\n                console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);\n                console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);\n                reject(new Error(`${processName} process failed with code ${code}`));\n            }\n        });\n        childProcess.on('error', (error)=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);\n            reject(new Error(`${processName} process error: ${error.message}`));\n        });\n        // Set timeout for individual process (60 seconds)\n        setTimeout(()=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);\n            childProcess.kill('SIGTERM');\n            reject(new Error(`${processName} process timed out after 60 seconds`));\n        }, 60000);\n    });\n};\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    // Check circuit breaker\n    if (isCircuitBreakerOpen(ticker)) {\n        return res.status(503).json({\n            error: 'Service temporarily unavailable due to repeated failures',\n            retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)\n        });\n    }\n    // Check if already processing\n    const currentProcessing = processing.get(ticker);\n    if (currentProcessing?.active) {\n        return res.status(429).json({\n            error: 'Already processing this symbol',\n            retryAfter: 15\n        });\n    }\n    // Set processing flag\n    processing.set(ticker, {\n        active: true,\n        startTime: Date.now()\n    });\n    try {\n        console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);\n        // Paths to service scripts\n        const lstmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_service.py');\n        const mfiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'mfi_service.py');\n        const bollingerScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'bollinger_service.py');\n        const rsiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'rsi_service.py');\n        const industryScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'industry_regression_service.py');\n        const garchScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'garch_service.py');\n        // Validate required script paths exist\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n            throw new Error('LSTM service script not found');\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath)) {\n            throw new Error('MFI service script not found');\n        }\n        // Execute all processes in parallel with graceful error handling\n        const [lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, garchResult] = await Promise.allSettled([\n            executeProcess(lstmScriptPath, ticker, 'LSTM'),\n            executeProcess(mfiScriptPath, ticker, 'MFI'),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n        ]);\n        // Extract results with error handling\n        const finalLSTMResult = lstmResult.status === 'fulfilled' ? lstmResult.value : null;\n        const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n        const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n        const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n        const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n        const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n        // Log any service failures\n        if (lstmResult.status === 'rejected') console.error(`[LSTM_ERROR] ${ticker}:`, lstmResult.reason);\n        if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n        if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n        if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n        if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n        if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n        // Add Korean summary to LSTM result\n        if (finalLSTMResult?.predictions) {\n            finalLSTMResult.summary_ko = getKoreanSummary(finalLSTMResult.predictions);\n        }\n        // Calculate technical majority vote for traffic light\n        const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n        const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n        const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n        // Save fine-tuning data (async, don't wait for completion)\n        saveFinetuningData(ticker, finalLSTMResult, finalMFIResult, finalBollingerResult, finalRSIResult, finalIndustryResult, finalGARCHResult).catch((error)=>{\n            console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n        });\n        // Merge results with enhanced structure\n        const mergedResult = {\n            lstm: finalLSTMResult,\n            mfi: finalMFIResult,\n            bollinger: finalBollingerResult,\n            rsi: finalRSIResult,\n            industry: finalIndustryResult,\n            garch: finalGARCHResult,\n            traffic_lights: {\n                lstm: finalLSTMResult ? getTrafficLightColor(finalLSTMResult, 'LSTM') : 'red',\n                technical: technicalColor,\n                sentiment: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                volume: 'inactive',\n                risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive'\n            }\n        };\n        // Write to persistence file\n        try {\n            const resultDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'lstm_results');\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(resultDir)) {\n                fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(resultDir, {\n                    recursive: true\n                });\n            }\n            const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '');\n            const resultFile = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultDir, `${ticker}_${timestamp}.json`);\n            fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(resultFile, JSON.stringify(mergedResult, null, 2), 'utf-8');\n            console.log(`[PERSISTENCE] Saved results to ${ticker}_${timestamp}.json`);\n        } catch (writeError) {\n            console.error(`[LSTM_SIMPLE_API] Failed to write result file for ${ticker}:`, writeError);\n        }\n        console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker} with ${Object.keys(mergedResult.traffic_lights).length} traffic lights`);\n        // Return the merged result\n        res.status(200).json(mergedResult);\n    } catch (error) {\n        console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);\n        // Record failure for circuit breaker\n        recordFailure(ticker);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Prediction failed',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    } finally{\n        // Clean up processing flag\n        processing.delete(ticker);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_prediction_simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();