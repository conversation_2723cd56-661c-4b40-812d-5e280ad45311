"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_prediction_simple";
exports.ids = ["pages/api/lstm_prediction_simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_prediction_simple.ts */ \"(api)/./src/pages/api/lstm_prediction_simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_prediction_simple\",\n        pathname: \"/api/lstm_prediction_simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_prediction_simple.ts":
/*!*************************************************!*\
  !*** ./src/pages/api/lstm_prediction_simple.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Per-symbol mutex using in-memory Map with timestamps\nconst processing = new Map();\n// Circuit breaker pattern - track failures per symbol\nconst failureCount = new Map();\nconst lastFailureTime = new Map();\nconst FAILURE_THRESHOLD = 3;\nconst CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes\n// Cleanup stale processing entries (older than 5 minutes)\nconst cleanupStaleProcessing = ()=>{\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    for (const [symbol, info] of processing.entries()){\n        if (info.active && now - info.startTime > fiveMinutes) {\n            console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);\n            processing.delete(symbol);\n        }\n    }\n};\n// Check if circuit breaker is open for a symbol\nconst isCircuitBreakerOpen = (symbol)=>{\n    const failures = failureCount.get(symbol) || 0;\n    const lastFailure = lastFailureTime.get(symbol) || 0;\n    const now = Date.now();\n    if (failures >= FAILURE_THRESHOLD) {\n        if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {\n            return true; // Circuit breaker is open\n        } else {\n            // Reset circuit breaker after timeout\n            failureCount.delete(symbol);\n            lastFailureTime.delete(symbol);\n            return false;\n        }\n    }\n    return false;\n};\n// Record a failure for circuit breaker\nconst recordFailure = (symbol)=>{\n    const failures = (failureCount.get(symbol) || 0) + 1;\n    failureCount.set(symbol, failures);\n    lastFailureTime.set(symbol, Date.now());\n    console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);\n};\n// Run cleanup every minute\nsetInterval(cleanupStaleProcessing, 60 * 1000);\n// Korean summary mapping\nconst getKoreanSummary = (predictions)=>{\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    if (correctCount === 2) {\n        return \"모두 예측 성공 결과 : green\";\n    } else if (correctCount === 1) {\n        return \"2일 예측 중 1일 예측 실패 결과 : yellow\";\n    } else {\n        return \"모두 예측 실패 결과 : red\";\n    }\n};\n// Traffic light color determination\nconst getTrafficLightColor = (result, processType)=>{\n    if (processType === 'LSTM') {\n        if (result.predictions && Array.isArray(result.predictions)) {\n            const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            if (correctCount === 2) return 'green';\n            if (correctCount === 1) return 'yellow';\n            return 'red';\n        }\n        // Fallback to result_color if available\n        return result.result_color || result.color || 'yellow';\n    } else if (processType === 'MFI') {\n        return result.traffic_light || result.color || 'yellow';\n    }\n    return 'yellow';\n};\n// Calculate LSTM accuracy percentage\nconst calculateLSTMAccuracy = (predictions)=>{\n    if (!Array.isArray(predictions) || predictions.length === 0) return 0;\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    return Math.round(correctCount / predictions.length * 100);\n};\n// Get LSTM pred_prob_up value\nconst getLSTMPredProbUp = (result)=>{\n    if (result.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {\n        return result.predictions[0].pred_prob_up || 0;\n    }\n    return result.pred_prob_up || 0;\n};\n// Get MFI numerical value\nconst getMFIValue = (result)=>{\n    return result.mfi_14 || result.mfi || 0;\n};\n// Get service-specific values for logging and fine-tuning\nconst getBollingerValue = (result)=>{\n    return result.percent_b || result.bollinger_position || result.position || 0;\n};\nconst getRSIValue = (result)=>{\n    return result.rsi_14 || result.rsi || 0;\n};\nconst getIndustryValue = (result)=>{\n    return result.industry || result.industry_sentiment || result.sentiment || 'neutral';\n};\nconst getGARCHValue = (result)=>{\n    return result.var_pct || result.volatility_forecast || result.volatility || 0;\n};\n// Get traffic light color from service results\nconst getServiceTrafficLight = (result)=>{\n    return result.traffic_light || result.color || 'yellow';\n};\n// Majority vote logic for technical analysis (MFI, Bollinger, RSI)\nconst getTechnicalMajorityVote = (mfiColor, bollingerColor, rsiColor)=>{\n    const colors = [\n        mfiColor,\n        bollingerColor,\n        rsiColor\n    ];\n    const colorCounts = {\n        green: colors.filter((c)=>c === 'green').length,\n        yellow: colors.filter((c)=>c === 'yellow').length,\n        red: colors.filter((c)=>c === 'red').length\n    };\n    // Return the color with the highest count\n    if (colorCounts.green >= 2) return 'green';\n    if (colorCounts.red >= 2) return 'red';\n    return 'yellow'; // Default to yellow if no majority or all different\n};\n// Save fine-tuning data with enhanced LSTM and all service results\nconst saveFinetuningData = async (ticker, lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, garchResult)=>{\n    try {\n        const finetuningDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'finetuning');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(finetuningDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(finetuningDir, {\n                recursive: true\n            });\n        }\n        const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\\..+/, '').replace('T', '_');\n        const filename = `${ticker}_${timestamp}.json`;\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(finetuningDir, filename);\n        // Extract LSTM metrics including pred_prob_up\n        const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;\n        const lstmPredProbUp = lstmResult ? getLSTMPredProbUp(lstmResult) : 0;\n        const lstmColor = lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red';\n        // Extract service metrics\n        const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;\n        const mfiColor = mfiResult ? getTrafficLightColor(mfiResult, 'MFI') : 'red';\n        const bollingerValue = bollingerResult ? getBollingerValue(bollingerResult) : 0;\n        const bollingerColor = bollingerResult ? getServiceTrafficLight(bollingerResult) : 'red';\n        const rsiValue = rsiResult ? getRSIValue(rsiResult) : 0;\n        const rsiColor = rsiResult ? getServiceTrafficLight(rsiResult) : 'red';\n        const industryValue = industryResult ? getIndustryValue(industryResult) : 'unknown';\n        const industryColor = industryResult ? getServiceTrafficLight(industryResult) : 'red';\n        const garchValue = garchResult ? getGARCHValue(garchResult) : 0;\n        const garchColor = garchResult ? getServiceTrafficLight(garchResult) : 'red';\n        // Calculate technical majority vote\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        const finetuningData = {\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"\"\n                },\n                {\n                    role: \"user\",\n                    content: `[LSTM accuracy: ${lstmAccuracy}%, pred_prob_up: ${lstmPredProbUp}, Traffic light: ${lstmColor.toUpperCase()}] [Technical (MFI: ${mfiValue}, Bollinger: ${bollingerValue}, RSI: ${rsiValue}), Traffic light: ${technicalColor.toUpperCase()}] [Sentiment (Industry: ${industryValue}), Traffic light: ${industryColor.toUpperCase()}] [Risk (GARCH: ${garchValue}), Traffic light: ${garchColor.toUpperCase()}]`\n                },\n                {\n                    role: \"assistant\",\n                    content: \"\"\n                }\n            ]\n        };\n        fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');\n        console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);\n    } catch (error) {\n        console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n    }\n};\n// Execute a process and return its result with enhanced logging\nconst executeProcess = (scriptPath, ticker, processName)=>{\n    return new Promise((resolve, reject)=>{\n        const servicesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services');\n        const startTime = Date.now();\n        console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);\n        const childProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            scriptPath,\n            ticker\n        ], {\n            cwd: servicesDir,\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ],\n            env: {\n                ...process.env,\n                PYTHONUNBUFFERED: '1'\n            }\n        });\n        let stdout = '';\n        let stderr = '';\n        childProcess.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        childProcess.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        childProcess.on('close', (code)=>{\n            const executionTime = Date.now() - startTime;\n            console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);\n            if (code === 0) {\n                try {\n                    // Parse the JSON output from the last line\n                    const lines = stdout.trim().split('\\n').filter((line)=>line.trim());\n                    // Find the line that looks like JSON (starts with '{')\n                    let jsonLine = null;\n                    for(let i = lines.length - 1; i >= 0; i--){\n                        const line = lines[i].trim();\n                        if (line.startsWith('{')) {\n                            jsonLine = line;\n                            break;\n                        }\n                    }\n                    if (!jsonLine) {\n                        throw new Error(`No JSON output found in ${processName} stdout`);\n                    }\n                    const jsonOutput = JSON.parse(jsonLine);\n                    // Enhanced result logging\n                    if (processName === 'LSTM') {\n                        const accuracy = calculateLSTMAccuracy(jsonOutput.predictions || []);\n                        const predProbUp = getLSTMPredProbUp(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');\n                        console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, pred_prob_up: ${predProbUp}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'MFI') {\n                        const mfiValue = getMFIValue(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');\n                        console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'BOLLINGER') {\n                        const bollingerValue = getBollingerValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[BOLLINGER_RESULT] ${ticker} - Percent B: ${bollingerValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'RSI') {\n                        const rsiValue = getRSIValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[RSI_RESULT] ${ticker} - RSI Value: ${rsiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'INDUSTRY') {\n                        const industryValue = getIndustryValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[INDUSTRY_RESULT] ${ticker} - Industry: ${industryValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'GARCH') {\n                        const garchValue = getGARCHValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[GARCH_RESULT] ${ticker} - VaR %: ${garchValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'CAPM') {\n                        const capmBeta = jsonOutput.beta_market || 0;\n                        const capmR2 = jsonOutput.r2_market || 0;\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[CAPM_RESULT] ${ticker} - Beta: ${capmBeta}, R²: ${capmR2}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    }\n                    resolve(jsonOutput);\n                } catch (parseError) {\n                    console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);\n                    reject(new Error(`Failed to parse ${processName} output: ${parseError}`));\n                }\n            } else {\n                console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);\n                console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);\n                reject(new Error(`${processName} process failed with code ${code}`));\n            }\n        });\n        childProcess.on('error', (error)=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);\n            reject(new Error(`${processName} process error: ${error.message}`));\n        });\n        // Set timeout for individual process (60 seconds)\n        setTimeout(()=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);\n            childProcess.kill('SIGTERM');\n            reject(new Error(`${processName} process timed out after 60 seconds`));\n        }, 60000);\n    });\n};\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    // Check circuit breaker\n    if (isCircuitBreakerOpen(ticker)) {\n        return res.status(503).json({\n            error: 'Service temporarily unavailable due to repeated failures',\n            retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)\n        });\n    }\n    // Check if already processing\n    const currentProcessing = processing.get(ticker);\n    if (currentProcessing?.active) {\n        return res.status(429).json({\n            error: 'Already processing this symbol',\n            retryAfter: 15\n        });\n    }\n    // Set processing flag\n    processing.set(ticker, {\n        active: true,\n        startTime: Date.now()\n    });\n    try {\n        console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);\n        // Check if this is a staged execution request\n        const { stage } = req.query;\n        // Paths to service scripts\n        const lstmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_service.py');\n        const mfiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'mfi_service.py');\n        const bollingerScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'bollinger_service.py');\n        const rsiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'rsi_service.py');\n        const industryScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'industry_regression_service.py');\n        const garchScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'garch_service.py');\n        const capmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'capm_service.py');\n        // Staged execution: Phase 1 (Fast services) vs Phase 2 (LSTM)\n        if (stage === 'phase1') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 1 (fast services) for ${ticker}`);\n            // Execute Phase 1: Technical, Industry, Market, Volatility services in parallel\n            const [mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath) ? executeProcess(mfiScriptPath, ticker, 'MFI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n            ]);\n            // Extract results with error handling\n            const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n            const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n            const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n            const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n            const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n            const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n            // Log any service failures\n            if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n            if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n            if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n            if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n            if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n            if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n            // Calculate technical majority vote for traffic light\n            const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n            const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n            const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n            const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n            console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n            // Phase 1 result structure\n            const phase1Result = {\n                phase: 1,\n                mfi: finalMFIResult,\n                bollinger: finalBollingerResult,\n                rsi: finalRSIResult,\n                industry: finalIndustryResult,\n                capm: finalCAPMResult,\n                garch: finalGARCHResult,\n                traffic_lights: {\n                    technical: technicalColor,\n                    industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                    market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                    risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive' // Light 4: Volatility Risk\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 1 completed successfully for ${ticker}`);\n            return res.status(200).json(phase1Result);\n        } else if (stage === 'phase2') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 2 (LSTM) for ${ticker}`);\n            // Validate LSTM script path exists\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n                throw new Error('LSTM service script not found');\n            }\n            // Execute Phase 2: LSTM service only\n            const lstmResult = await executeProcess(lstmScriptPath, ticker, 'LSTM');\n            // Add Korean summary to LSTM result\n            if (lstmResult?.predictions) {\n                lstmResult.summary_ko = getKoreanSummary(lstmResult.predictions);\n            }\n            // Phase 2 result structure\n            const phase2Result = {\n                phase: 2,\n                lstm: lstmResult,\n                traffic_lights: {\n                    neural: lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 2 completed successfully for ${ticker}`);\n            return res.status(200).json(phase2Result);\n        }\n        // Legacy mode: Execute all processes in parallel (backward compatibility)\n        console.log(`[LSTM_SIMPLE_API] Starting legacy mode (all services) for ${ticker}`);\n        // Validate required script paths exist\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n            throw new Error('LSTM service script not found');\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath)) {\n            throw new Error('MFI service script not found');\n        }\n        // Execute all processes in parallel with graceful error handling\n        const [lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n            executeProcess(lstmScriptPath, ticker, 'LSTM'),\n            executeProcess(mfiScriptPath, ticker, 'MFI'),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n        ]);\n        // Extract results with error handling\n        const finalLSTMResult = lstmResult.status === 'fulfilled' ? lstmResult.value : null;\n        const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n        const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n        const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n        const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n        const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n        const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n        // Log any service failures\n        if (lstmResult.status === 'rejected') console.error(`[LSTM_ERROR] ${ticker}:`, lstmResult.reason);\n        if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n        if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n        if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n        if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n        if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n        if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n        // Add Korean summary to LSTM result\n        if (finalLSTMResult?.predictions) {\n            finalLSTMResult.summary_ko = getKoreanSummary(finalLSTMResult.predictions);\n        }\n        // Calculate technical majority vote for traffic light\n        const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n        const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n        const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n        // Save fine-tuning data (async, don't wait for completion)\n        saveFinetuningData(ticker, finalLSTMResult, finalMFIResult, finalBollingerResult, finalRSIResult, finalIndustryResult, finalGARCHResult).catch((error)=>{\n            console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n        });\n        // Merge results with enhanced structure (legacy mode)\n        const mergedResult = {\n            lstm: finalLSTMResult,\n            mfi: finalMFIResult,\n            bollinger: finalBollingerResult,\n            rsi: finalRSIResult,\n            industry: finalIndustryResult,\n            capm: finalCAPMResult,\n            garch: finalGARCHResult,\n            traffic_lights: {\n                technical: technicalColor,\n                industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive',\n                neural: finalLSTMResult ? getTrafficLightColor(finalLSTMResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n            }\n        };\n        // Write to persistence file\n        try {\n            const resultDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'lstm_results');\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(resultDir)) {\n                fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(resultDir, {\n                    recursive: true\n                });\n            }\n            const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '');\n            const resultFile = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultDir, `${ticker}_${timestamp}.json`);\n            fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(resultFile, JSON.stringify(mergedResult, null, 2), 'utf-8');\n            console.log(`[PERSISTENCE] Saved results to ${ticker}_${timestamp}.json`);\n        } catch (writeError) {\n            console.error(`[LSTM_SIMPLE_API] Failed to write result file for ${ticker}:`, writeError);\n        }\n        console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker} with ${Object.keys(mergedResult.traffic_lights).length} traffic lights`);\n        // Return the merged result\n        res.status(200).json(mergedResult);\n    } catch (error) {\n        console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);\n        // Record failure for circuit breaker\n        recordFailure(ticker);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Prediction failed',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    } finally{\n        // Clean up processing flag\n        processing.delete(ticker);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_prediction_simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();