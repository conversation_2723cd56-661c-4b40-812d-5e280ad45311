"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_prediction_simple";
exports.ids = ["pages/api/lstm_prediction_simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_prediction_simple.ts */ \"(api)/./src/pages/api/lstm_prediction_simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_prediction_simple\",\n        pathname: \"/api/lstm_prediction_simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_prediction_simple.ts":
/*!*************************************************!*\
  !*** ./src/pages/api/lstm_prediction_simple.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Per-symbol mutex using in-memory Map with timestamps\nconst processing = new Map();\n// Circuit breaker pattern - track failures per symbol\nconst failureCount = new Map();\nconst lastFailureTime = new Map();\nconst FAILURE_THRESHOLD = 3;\nconst CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes\n// Cleanup stale processing entries (older than 5 minutes)\nconst cleanupStaleProcessing = ()=>{\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    for (const [symbol, info] of processing.entries()){\n        if (info.active && now - info.startTime > fiveMinutes) {\n            console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);\n            processing.delete(symbol);\n        }\n    }\n};\n// Check if circuit breaker is open for a symbol\nconst isCircuitBreakerOpen = (symbol)=>{\n    const failures = failureCount.get(symbol) || 0;\n    const lastFailure = lastFailureTime.get(symbol) || 0;\n    const now = Date.now();\n    if (failures >= FAILURE_THRESHOLD) {\n        if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {\n            return true; // Circuit breaker is open\n        } else {\n            // Reset circuit breaker after timeout\n            failureCount.delete(symbol);\n            lastFailureTime.delete(symbol);\n            return false;\n        }\n    }\n    return false;\n};\n// Record a failure for circuit breaker\nconst recordFailure = (symbol)=>{\n    const failures = (failureCount.get(symbol) || 0) + 1;\n    failureCount.set(symbol, failures);\n    lastFailureTime.set(symbol, Date.now());\n    console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);\n};\n// Run cleanup every minute\nsetInterval(cleanupStaleProcessing, 60 * 1000);\n// Korean summary mapping\nconst getKoreanSummary = (predictions)=>{\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    if (correctCount === 2) {\n        return \"모두 예측 성공 결과 : green\";\n    } else if (correctCount === 1) {\n        return \"2일 예측 중 1일 예측 실패 결과 : yellow\";\n    } else {\n        return \"모두 예측 실패 결과 : red\";\n    }\n};\n// Traffic light color determination\nconst getTrafficLightColor = (result, processType)=>{\n    if (processType === 'LSTM') {\n        if (result.predictions && Array.isArray(result.predictions)) {\n            const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            if (correctCount === 2) return 'green';\n            if (correctCount === 1) return 'yellow';\n            return 'red';\n        }\n        // Fallback to result_color if available\n        return result.result_color || result.color || 'yellow';\n    } else if (processType === 'MFI') {\n        return result.traffic_light || result.color || 'yellow';\n    }\n    return 'yellow';\n};\n// Calculate LSTM accuracy percentage\nconst calculateLSTMAccuracy = (predictions)=>{\n    if (!Array.isArray(predictions) || predictions.length === 0) return 0;\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    return Math.round(correctCount / predictions.length * 100);\n};\n// Get MFI numerical value\nconst getMFIValue = (result)=>{\n    return result.mfi_14 || result.mfi || 0;\n};\n// Save fine-tuning data\nconst saveFinetuningData = async (ticker, lstmResult, mfiResult)=>{\n    try {\n        const finetuningDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'finetuning');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(finetuningDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(finetuningDir, {\n                recursive: true\n            });\n        }\n        const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\\..+/, '').replace('T', '_');\n        const filename = `${ticker}_${timestamp}.json`;\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(finetuningDir, filename);\n        // Extract metrics\n        const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;\n        const lstmColor = lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red';\n        const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;\n        const mfiColor = mfiResult ? getTrafficLightColor(mfiResult, 'MFI') : 'red';\n        const finetuningData = {\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"\"\n                },\n                {\n                    role: \"user\",\n                    content: `[LSTM accuracy: ${lstmAccuracy}%, Traffic light: ${lstmColor.toUpperCase()}] [MFI value: ${mfiValue}, Traffic light: ${mfiColor.toUpperCase()}]`\n                },\n                {\n                    role: \"assistant\",\n                    content: \"\"\n                }\n            ]\n        };\n        fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');\n        console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);\n    } catch (error) {\n        console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n    }\n};\n// Execute a process and return its result with enhanced logging\nconst executeProcess = (scriptPath, ticker, processName)=>{\n    return new Promise((resolve, reject)=>{\n        const servicesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services');\n        const startTime = Date.now();\n        console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);\n        const childProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            scriptPath,\n            ticker\n        ], {\n            cwd: servicesDir,\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ],\n            env: {\n                ...process.env,\n                PYTHONUNBUFFERED: '1'\n            }\n        });\n        let stdout = '';\n        let stderr = '';\n        childProcess.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        childProcess.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        childProcess.on('close', (code)=>{\n            const executionTime = Date.now() - startTime;\n            console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);\n            if (code === 0) {\n                try {\n                    // Parse the JSON output from the last line\n                    const lines = stdout.trim().split('\\n').filter((line)=>line.trim());\n                    // Find the line that looks like JSON (starts with '{')\n                    let jsonLine = null;\n                    for(let i = lines.length - 1; i >= 0; i--){\n                        const line = lines[i].trim();\n                        if (line.startsWith('{')) {\n                            jsonLine = line;\n                            break;\n                        }\n                    }\n                    if (!jsonLine) {\n                        throw new Error(`No JSON output found in ${processName} stdout`);\n                    }\n                    const jsonOutput = JSON.parse(jsonLine);\n                    // Enhanced result logging\n                    if (processName === 'LSTM') {\n                        const accuracy = calculateLSTMAccuracy(jsonOutput.predictions || []);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');\n                        console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'MFI') {\n                        const mfiValue = getMFIValue(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');\n                        console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    }\n                    resolve(jsonOutput);\n                } catch (parseError) {\n                    console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);\n                    reject(new Error(`Failed to parse ${processName} output: ${parseError}`));\n                }\n            } else {\n                console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);\n                console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);\n                reject(new Error(`${processName} process failed with code ${code}`));\n            }\n        });\n        childProcess.on('error', (error)=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);\n            reject(new Error(`${processName} process error: ${error.message}`));\n        });\n        // Set timeout for individual process (60 seconds)\n        setTimeout(()=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);\n            childProcess.kill('SIGTERM');\n            reject(new Error(`${processName} process timed out after 60 seconds`));\n        }, 60000);\n    });\n};\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    // Check circuit breaker\n    if (isCircuitBreakerOpen(ticker)) {\n        return res.status(503).json({\n            error: 'Service temporarily unavailable due to repeated failures',\n            retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)\n        });\n    }\n    // Check if already processing\n    const currentProcessing = processing.get(ticker);\n    if (currentProcessing?.active) {\n        return res.status(429).json({\n            error: 'Already processing this symbol',\n            retryAfter: 15\n        });\n    }\n    // Set processing flag\n    processing.set(ticker, {\n        active: true,\n        startTime: Date.now()\n    });\n    try {\n        console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);\n        // Paths to service scripts\n        const lstmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_service.py');\n        const mfiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'mfi_service.py');\n        // Validate script paths exist\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n            throw new Error('LSTM service script not found');\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath)) {\n            throw new Error('MFI service script not found');\n        }\n        // Execute both processes in parallel\n        const [lstmResult, mfiResult] = await Promise.all([\n            executeProcess(lstmScriptPath, ticker, 'LSTM'),\n            executeProcess(mfiScriptPath, ticker, 'MFI')\n        ]);\n        // Add Korean summary to LSTM result\n        if (lstmResult.predictions) {\n            lstmResult.summary_ko = getKoreanSummary(lstmResult.predictions);\n        }\n        // Save fine-tuning data (async, don't wait for completion)\n        saveFinetuningData(ticker, lstmResult, mfiResult).catch((error)=>{\n            console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n        });\n        // Merge results\n        const mergedResult = {\n            lstm: lstmResult,\n            mfi: mfiResult\n        };\n        // Write to persistence file\n        try {\n            const resultDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'lstm_results');\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(resultDir)) {\n                fs__WEBPACK_IMPORTED_MODULE_2___default().mkdirSync(resultDir, {\n                    recursive: true\n                });\n            }\n            const resultFile = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultDir, `${ticker}_20250605.json`);\n            fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(resultFile, JSON.stringify(mergedResult, null, 2), 'utf-8');\n        } catch (writeError) {\n            console.error(`[LSTM_SIMPLE_API] Failed to write result file for ${ticker}:`, writeError);\n        }\n        console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker}`);\n        // Return the merged result\n        res.status(200).json(mergedResult);\n    } catch (error) {\n        console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);\n        // Record failure for circuit breaker\n        recordFailure(ticker);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Prediction failed',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    } finally{\n        // Clean up processing flag\n        processing.delete(ticker);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL2xzdG1fcHJlZGljdGlvbl9zaW1wbGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQztBQUNkO0FBQ0o7QUFFcEIsdURBQXVEO0FBQ3ZELE1BQU1HLGFBQWEsSUFBSUM7QUFFdkIsc0RBQXNEO0FBQ3RELE1BQU1DLGVBQWUsSUFBSUQ7QUFDekIsTUFBTUUsa0JBQWtCLElBQUlGO0FBQzVCLE1BQU1HLG9CQUFvQjtBQUMxQixNQUFNQywwQkFBMEIsSUFBSSxLQUFLLE1BQU0sWUFBWTtBQUUzRCwwREFBMEQ7QUFDMUQsTUFBTUMseUJBQXlCO0lBQzdCLE1BQU1DLE1BQU1DLEtBQUtELEdBQUc7SUFDcEIsTUFBTUUsY0FBYyxJQUFJLEtBQUs7SUFFN0IsS0FBSyxNQUFNLENBQUNDLFFBQVFDLEtBQUssSUFBSVgsV0FBV1ksT0FBTyxHQUFJO1FBQ2pELElBQUlELEtBQUtFLE1BQU0sSUFBSSxNQUFPRixLQUFLRyxTQUFTLEdBQUlMLGFBQWE7WUFDdkRNLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlEQUF5RCxFQUFFTixRQUFRO1lBQ2hGVixXQUFXaUIsTUFBTSxDQUFDUDtRQUNwQjtJQUNGO0FBQ0Y7QUFFQSxnREFBZ0Q7QUFDaEQsTUFBTVEsdUJBQXVCLENBQUNSO0lBQzVCLE1BQU1TLFdBQVdqQixhQUFha0IsR0FBRyxDQUFDVixXQUFXO0lBQzdDLE1BQU1XLGNBQWNsQixnQkFBZ0JpQixHQUFHLENBQUNWLFdBQVc7SUFDbkQsTUFBTUgsTUFBTUMsS0FBS0QsR0FBRztJQUVwQixJQUFJWSxZQUFZZixtQkFBbUI7UUFDakMsSUFBSUcsTUFBTWMsY0FBY2hCLHlCQUF5QjtZQUMvQyxPQUFPLE1BQU0sMEJBQTBCO1FBQ3pDLE9BQU87WUFDTCxzQ0FBc0M7WUFDdENILGFBQWFlLE1BQU0sQ0FBQ1A7WUFDcEJQLGdCQUFnQmMsTUFBTSxDQUFDUDtZQUN2QixPQUFPO1FBQ1Q7SUFDRjtJQUNBLE9BQU87QUFDVDtBQUVBLHVDQUF1QztBQUN2QyxNQUFNWSxnQkFBZ0IsQ0FBQ1o7SUFDckIsTUFBTVMsV0FBVyxDQUFDakIsYUFBYWtCLEdBQUcsQ0FBQ1YsV0FBVyxLQUFLO0lBQ25EUixhQUFhcUIsR0FBRyxDQUFDYixRQUFRUztJQUN6QmhCLGdCQUFnQm9CLEdBQUcsQ0FBQ2IsUUFBUUYsS0FBS0QsR0FBRztJQUNwQ1EsUUFBUUMsR0FBRyxDQUFDLENBQUMsbUNBQW1DLEVBQUVHLFNBQVMsS0FBSyxFQUFFVCxRQUFRO0FBQzVFO0FBRUEsMkJBQTJCO0FBQzNCYyxZQUFZbEIsd0JBQXdCLEtBQUs7QUFFekMseUJBQXlCO0FBQ3pCLE1BQU1tQixtQkFBbUIsQ0FBQ0M7SUFDeEIsTUFBTUMsZUFBZUQsWUFBWUUsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxlQUFlLEtBQUtELEVBQUVFLFlBQVksRUFBRUMsTUFBTTtJQUV6RixJQUFJTCxpQkFBaUIsR0FBRztRQUN0QixPQUFPO0lBQ1QsT0FBTyxJQUFJQSxpQkFBaUIsR0FBRztRQUM3QixPQUFPO0lBQ1QsT0FBTztRQUNMLE9BQU87SUFDVDtBQUNGO0FBRUEsb0NBQW9DO0FBQ3BDLE1BQU1NLHVCQUF1QixDQUFDQyxRQUFhQztJQUN6QyxJQUFJQSxnQkFBZ0IsUUFBUTtRQUMxQixJQUFJRCxPQUFPUixXQUFXLElBQUlVLE1BQU1DLE9BQU8sQ0FBQ0gsT0FBT1IsV0FBVyxHQUFHO1lBQzNELE1BQU1DLGVBQWVPLE9BQU9SLFdBQVcsQ0FBQ0UsTUFBTSxDQUFDLENBQUNDLElBQVdBLEVBQUVDLGVBQWUsS0FBS0QsRUFBRUUsWUFBWSxFQUFFQyxNQUFNO1lBQ3ZHLElBQUlMLGlCQUFpQixHQUFHLE9BQU87WUFDL0IsSUFBSUEsaUJBQWlCLEdBQUcsT0FBTztZQUMvQixPQUFPO1FBQ1Q7UUFDQSx3Q0FBd0M7UUFDeEMsT0FBT08sT0FBT0ksWUFBWSxJQUFJSixPQUFPSyxLQUFLLElBQUk7SUFDaEQsT0FBTyxJQUFJSixnQkFBZ0IsT0FBTztRQUNoQyxPQUFPRCxPQUFPTSxhQUFhLElBQUlOLE9BQU9LLEtBQUssSUFBSTtJQUNqRDtJQUNBLE9BQU87QUFDVDtBQUVBLHFDQUFxQztBQUNyQyxNQUFNRSx3QkFBd0IsQ0FBQ2Y7SUFDN0IsSUFBSSxDQUFDVSxNQUFNQyxPQUFPLENBQUNYLGdCQUFnQkEsWUFBWU0sTUFBTSxLQUFLLEdBQUcsT0FBTztJQUNwRSxNQUFNTCxlQUFlRCxZQUFZRSxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLGVBQWUsS0FBS0QsRUFBRUUsWUFBWSxFQUFFQyxNQUFNO0lBQ3pGLE9BQU9VLEtBQUtDLEtBQUssQ0FBQyxlQUFnQmpCLFlBQVlNLE1BQU0sR0FBSTtBQUMxRDtBQUVBLDBCQUEwQjtBQUMxQixNQUFNWSxjQUFjLENBQUNWO0lBQ25CLE9BQU9BLE9BQU9XLE1BQU0sSUFBSVgsT0FBT1ksR0FBRyxJQUFJO0FBQ3hDO0FBRUEsd0JBQXdCO0FBQ3hCLE1BQU1DLHFCQUFxQixPQUFPQyxRQUFnQkMsWUFBaUJDO0lBQ2pFLElBQUk7UUFDRixNQUFNQyxnQkFBZ0JyRCxnREFBUyxDQUFDdUQsUUFBUUMsR0FBRyxJQUFJLE9BQU8sUUFBUTtRQUM5RCxJQUFJLENBQUN2RCxvREFBYSxDQUFDb0QsZ0JBQWdCO1lBQ2pDcEQsbURBQVksQ0FBQ29ELGVBQWU7Z0JBQUVNLFdBQVc7WUFBSztRQUNoRDtRQUVBLE1BQU1DLFlBQVksSUFBSWxELE9BQU9tRCxXQUFXLEdBQUdDLE9BQU8sQ0FBQyxTQUFTLElBQUlBLE9BQU8sQ0FBQyxRQUFRLElBQUlBLE9BQU8sQ0FBQyxLQUFLO1FBQ2pHLE1BQU1DLFdBQVcsR0FBR2IsT0FBTyxDQUFDLEVBQUVVLFVBQVUsS0FBSyxDQUFDO1FBQzlDLE1BQU1JLFdBQVdoRSxnREFBUyxDQUFDcUQsZUFBZVU7UUFFMUMsa0JBQWtCO1FBQ2xCLE1BQU1FLGVBQWVkLGFBQWFSLHNCQUFzQlEsV0FBV3ZCLFdBQVcsSUFBSSxFQUFFLElBQUk7UUFDeEYsTUFBTXNDLFlBQVlmLGFBQWFoQixxQkFBcUJnQixZQUFZLFVBQVU7UUFDMUUsTUFBTWdCLFdBQVdmLFlBQVlOLFlBQVlNLGFBQWE7UUFDdEQsTUFBTWdCLFdBQVdoQixZQUFZakIscUJBQXFCaUIsV0FBVyxTQUFTO1FBRXRFLE1BQU1pQixpQkFBaUI7WUFDckJDLFVBQVU7Z0JBQ1I7b0JBQUVDLE1BQU07b0JBQVVDLFNBQVM7Z0JBQUc7Z0JBQzlCO29CQUNFRCxNQUFNO29CQUNOQyxTQUFTLENBQUMsZ0JBQWdCLEVBQUVQLGFBQWEsa0JBQWtCLEVBQUVDLFVBQVVPLFdBQVcsR0FBRyxjQUFjLEVBQUVOLFNBQVMsaUJBQWlCLEVBQUVDLFNBQVNLLFdBQVcsR0FBRyxDQUFDLENBQUM7Z0JBQzVKO2dCQUNBO29CQUFFRixNQUFNO29CQUFhQyxTQUFTO2dCQUFHO2FBQ2xDO1FBQ0g7UUFFQXZFLHVEQUFnQixDQUFDK0QsVUFBVVcsS0FBS0MsU0FBUyxDQUFDUCxnQkFBZ0IsTUFBTSxJQUFJO1FBQ3BFcEQsUUFBUUMsR0FBRyxDQUFDLENBQUMsNENBQTRDLEVBQUU2QyxVQUFVO0lBQ3ZFLEVBQUUsT0FBT2MsT0FBTztRQUNkNUQsUUFBUTRELEtBQUssQ0FBQyxDQUFDLHNEQUFzRCxFQUFFM0IsT0FBTyxDQUFDLENBQUMsRUFBRTJCO0lBQ3BGO0FBQ0Y7QUFFQSxnRUFBZ0U7QUFDaEUsTUFBTUMsaUJBQWlCLENBQUNDLFlBQW9CN0IsUUFBZ0I4QjtJQUMxRCxPQUFPLElBQUlDLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0IsTUFBTUMsY0FBY3BGLGdEQUFTLENBQUN1RCxRQUFRQyxHQUFHLElBQUksT0FBTztRQUNwRCxNQUFNeEMsWUFBWU4sS0FBS0QsR0FBRztRQUUxQlEsUUFBUUMsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUU4RCxZQUFZLGFBQWEsRUFBRTlCLFFBQVE7UUFFN0UsTUFBTW1DLGVBQWV0RixvREFBS0EsQ0FBQyxVQUFVO1lBQUNnRjtZQUFZN0I7U0FBTyxFQUFFO1lBQ3pETSxLQUFLNEI7WUFDTEUsT0FBTztnQkFBQztnQkFBUTtnQkFBUTthQUFPO1lBQy9CQyxLQUFLO2dCQUFFLEdBQUdoQyxRQUFRZ0MsR0FBRztnQkFBRUMsa0JBQWtCO1lBQUk7UUFDL0M7UUFFQSxJQUFJQyxTQUFTO1FBQ2IsSUFBSUMsU0FBUztRQUViTCxhQUFhSSxNQUFNLENBQUNFLEVBQUUsQ0FBQyxRQUFRLENBQUNDO1lBQzlCSCxVQUFVRyxLQUFLQyxRQUFRO1FBQ3pCO1FBRUFSLGFBQWFLLE1BQU0sQ0FBQ0MsRUFBRSxDQUFDLFFBQVEsQ0FBQ0M7WUFDOUJGLFVBQVVFLEtBQUtDLFFBQVE7UUFDekI7UUFFQVIsYUFBYU0sRUFBRSxDQUFDLFNBQVMsQ0FBQ0c7WUFDeEIsTUFBTUMsZ0JBQWdCckYsS0FBS0QsR0FBRyxLQUFLTztZQUNuQ0MsUUFBUUMsR0FBRyxDQUFDLENBQUMsa0JBQWtCLEVBQUU4RCxZQUFZLG9CQUFvQixFQUFFOUIsT0FBTyxXQUFXLEVBQUU0QyxLQUFLLEVBQUUsRUFBRUMsY0FBYyxHQUFHLENBQUM7WUFFbEgsSUFBSUQsU0FBUyxHQUFHO2dCQUNkLElBQUk7b0JBQ0YsMkNBQTJDO29CQUMzQyxNQUFNRSxRQUFRUCxPQUFPUSxJQUFJLEdBQUdDLEtBQUssQ0FBQyxNQUFNcEUsTUFBTSxDQUFDcUUsQ0FBQUEsT0FBUUEsS0FBS0YsSUFBSTtvQkFFaEUsdURBQXVEO29CQUN2RCxJQUFJRyxXQUFXO29CQUNmLElBQUssSUFBSUMsSUFBSUwsTUFBTTlELE1BQU0sR0FBRyxHQUFHbUUsS0FBSyxHQUFHQSxJQUFLO3dCQUMxQyxNQUFNRixPQUFPSCxLQUFLLENBQUNLLEVBQUUsQ0FBQ0osSUFBSTt3QkFDMUIsSUFBSUUsS0FBS0csVUFBVSxDQUFDLE1BQU07NEJBQ3hCRixXQUFXRDs0QkFDWDt3QkFDRjtvQkFDRjtvQkFFQSxJQUFJLENBQUNDLFVBQVU7d0JBQ2IsTUFBTSxJQUFJRyxNQUFNLENBQUMsd0JBQXdCLEVBQUV2QixZQUFZLE9BQU8sQ0FBQztvQkFDakU7b0JBRUEsTUFBTXdCLGFBQWE3QixLQUFLOEIsS0FBSyxDQUFDTDtvQkFFOUIsMEJBQTBCO29CQUMxQixJQUFJcEIsZ0JBQWdCLFFBQVE7d0JBQzFCLE1BQU0wQixXQUFXL0Qsc0JBQXNCNkQsV0FBVzVFLFdBQVcsSUFBSSxFQUFFO3dCQUNuRSxNQUFNK0UsZUFBZXhFLHFCQUFxQnFFLFlBQVk7d0JBQ3REdkYsUUFBUUMsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFZ0MsT0FBTyxhQUFhLEVBQUV3RCxTQUFTLGtCQUFrQixFQUFFQyxhQUFhbEMsV0FBVyxHQUFHLGtCQUFrQixFQUFFc0IsY0FBYyxtQkFBbUIsQ0FBQztvQkFDbkssT0FBTyxJQUFJZixnQkFBZ0IsT0FBTzt3QkFDaEMsTUFBTWIsV0FBV3JCLFlBQVkwRDt3QkFDN0IsTUFBTUcsZUFBZXhFLHFCQUFxQnFFLFlBQVk7d0JBQ3REdkYsUUFBUUMsR0FBRyxDQUFDLENBQUMsYUFBYSxFQUFFZ0MsT0FBTyxjQUFjLEVBQUVpQixTQUFTLGlCQUFpQixFQUFFd0MsYUFBYWxDLFdBQVcsR0FBRyxrQkFBa0IsRUFBRXNCLGNBQWMsbUJBQW1CLENBQUM7b0JBQ2xLO29CQUVBYixRQUFRc0I7Z0JBQ1YsRUFBRSxPQUFPSSxZQUFZO29CQUNuQjNGLFFBQVE0RCxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVHLFlBQVksU0FBUyxFQUFFOUIsT0FBTyx3Q0FBd0MsRUFBRTZDLGNBQWMsV0FBVyxFQUFFYSxZQUFZO29CQUNqSXpCLE9BQU8sSUFBSW9CLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRXZCLFlBQVksU0FBUyxFQUFFNEIsWUFBWTtnQkFDekU7WUFDRixPQUFPO2dCQUNMM0YsUUFBUTRELEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRUcsWUFBWSxTQUFTLEVBQUU5QixPQUFPLDJDQUEyQyxFQUFFNkMsY0FBYyxlQUFlLEVBQUVELE1BQU07Z0JBQ2xJN0UsUUFBUTRELEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRUcsWUFBWSxTQUFTLEVBQUU5QixPQUFPLFVBQVUsQ0FBQyxFQUFFd0M7Z0JBQzdEUCxPQUFPLElBQUlvQixNQUFNLEdBQUd2QixZQUFZLDBCQUEwQixFQUFFYyxNQUFNO1lBQ3BFO1FBQ0Y7UUFFQVQsYUFBYU0sRUFBRSxDQUFDLFNBQVMsQ0FBQ2Q7WUFDeEIsTUFBTWtCLGdCQUFnQnJGLEtBQUtELEdBQUcsS0FBS087WUFDbkNDLFFBQVE0RCxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVHLFlBQVksU0FBUyxFQUFFOUIsT0FBTyx3Q0FBd0MsRUFBRTZDLGNBQWMsV0FBVyxFQUFFbEIsTUFBTWdDLE9BQU8sRUFBRTtZQUNwSTFCLE9BQU8sSUFBSW9CLE1BQU0sR0FBR3ZCLFlBQVksZ0JBQWdCLEVBQUVILE1BQU1nQyxPQUFPLEVBQUU7UUFDbkU7UUFFQSxrREFBa0Q7UUFDbERDLFdBQVc7WUFDVCxNQUFNZixnQkFBZ0JyRixLQUFLRCxHQUFHLEtBQUtPO1lBQ25DQyxRQUFRNEQsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFRyxZQUFZLFNBQVMsRUFBRTlCLE9BQU8sb0NBQW9DLEVBQUU2QyxjQUFjLEVBQUUsQ0FBQztZQUN2R1YsYUFBYTBCLElBQUksQ0FBQztZQUNsQjVCLE9BQU8sSUFBSW9CLE1BQU0sR0FBR3ZCLFlBQVksbUNBQW1DLENBQUM7UUFDdEUsR0FBRztJQUNMO0FBQ0Y7QUFFZSxlQUFlZ0MsUUFBUUMsR0FBbUIsRUFBRUMsR0FBb0I7SUFDN0UsSUFBSUQsSUFBSUUsTUFBTSxLQUFLLE9BQU87UUFDeEIsT0FBT0QsSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFeEMsT0FBTztRQUFxQjtJQUM1RDtJQUVBLE1BQU0sRUFBRWpFLE1BQU0sRUFBRSxHQUFHcUcsSUFBSUssS0FBSztJQUU1QixJQUFJLENBQUMxRyxVQUFVLE9BQU9BLFdBQVcsVUFBVTtRQUN6QyxPQUFPc0csSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFeEMsT0FBTztRQUFxQjtJQUM1RDtJQUVBLE1BQU0zQixTQUFTdEMsT0FBTzZELFdBQVc7SUFFakMsd0JBQXdCO0lBQ3hCLElBQUlyRCxxQkFBcUI4QixTQUFTO1FBQ2hDLE9BQU9nRSxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQzFCeEMsT0FBTztZQUNQMEMsWUFBWTNFLEtBQUs0RSxJQUFJLENBQUNqSCwwQkFBMEI7UUFDbEQ7SUFDRjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNa0gsb0JBQW9CdkgsV0FBV29CLEdBQUcsQ0FBQzRCO0lBQ3pDLElBQUl1RSxtQkFBbUIxRyxRQUFRO1FBQzdCLE9BQU9tRyxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQzFCeEMsT0FBTztZQUNQMEMsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxzQkFBc0I7SUFDdEJySCxXQUFXdUIsR0FBRyxDQUFDeUIsUUFBUTtRQUFFbkMsUUFBUTtRQUFNQyxXQUFXTixLQUFLRCxHQUFHO0lBQUc7SUFFN0QsSUFBSTtRQUNGUSxRQUFRQyxHQUFHLENBQUMsQ0FBQywwQ0FBMEMsRUFBRWdDLFFBQVE7UUFFakUsMkJBQTJCO1FBQzNCLE1BQU13RSxpQkFBaUIxSCxnREFBUyxDQUFDdUQsUUFBUUMsR0FBRyxJQUFJLE9BQU8sWUFBWTtRQUNuRSxNQUFNbUUsZ0JBQWdCM0gsZ0RBQVMsQ0FBQ3VELFFBQVFDLEdBQUcsSUFBSSxPQUFPLFlBQVk7UUFFbEUsOEJBQThCO1FBQzlCLElBQUksQ0FBQ3ZELG9EQUFhLENBQUN5SCxpQkFBaUI7WUFDbEMsTUFBTSxJQUFJbkIsTUFBTTtRQUNsQjtRQUVBLElBQUksQ0FBQ3RHLG9EQUFhLENBQUMwSCxnQkFBZ0I7WUFDakMsTUFBTSxJQUFJcEIsTUFBTTtRQUNsQjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNLENBQUNwRCxZQUFZQyxVQUFVLEdBQUcsTUFBTTZCLFFBQVEyQyxHQUFHLENBQUM7WUFDaEQ5QyxlQUFlNEMsZ0JBQWdCeEUsUUFBUTtZQUN2QzRCLGVBQWU2QyxlQUFlekUsUUFBUTtTQUN2QztRQUVELG9DQUFvQztRQUNwQyxJQUFJQyxXQUFXdkIsV0FBVyxFQUFFO1lBQzFCdUIsV0FBVzBFLFVBQVUsR0FBR2xHLGlCQUFpQndCLFdBQVd2QixXQUFXO1FBQ2pFO1FBRUEsMkRBQTJEO1FBQzNEcUIsbUJBQW1CQyxRQUFRQyxZQUFZQyxXQUFXMEUsS0FBSyxDQUFDakQsQ0FBQUE7WUFDdEQ1RCxRQUFRNEQsS0FBSyxDQUFDLENBQUMsc0RBQXNELEVBQUUzQixPQUFPLENBQUMsQ0FBQyxFQUFFMkI7UUFDcEY7UUFFQSxnQkFBZ0I7UUFDaEIsTUFBTWtELGVBQWU7WUFDbkJDLE1BQU03RTtZQUNOSCxLQUFLSTtRQUNQO1FBRUEsNEJBQTRCO1FBQzVCLElBQUk7WUFDRixNQUFNNkUsWUFBWWpJLGdEQUFTLENBQUN1RCxRQUFRQyxHQUFHLElBQUksUUFBUTtZQUNuRCxJQUFJLENBQUN2RCxvREFBYSxDQUFDZ0ksWUFBWTtnQkFDN0JoSSxtREFBWSxDQUFDZ0ksV0FBVztvQkFBRXRFLFdBQVc7Z0JBQUs7WUFDNUM7WUFDQSxNQUFNdUUsYUFBYWxJLGdEQUFTLENBQUNpSSxXQUFXLEdBQUcvRSxPQUFPLGNBQWMsQ0FBQztZQUNqRWpELHVEQUFnQixDQUFDaUksWUFBWXZELEtBQUtDLFNBQVMsQ0FBQ21ELGNBQWMsTUFBTSxJQUFJO1FBQ3RFLEVBQUUsT0FBT0ksWUFBWTtZQUNuQmxILFFBQVE0RCxLQUFLLENBQUMsQ0FBQyxrREFBa0QsRUFBRTNCLE9BQU8sQ0FBQyxDQUFDLEVBQUVpRjtRQUNoRjtRQUVBbEgsUUFBUUMsR0FBRyxDQUFDLENBQUMsd0RBQXdELEVBQUVnQyxRQUFRO1FBRS9FLDJCQUEyQjtRQUMzQmdFLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUNVO0lBRXZCLEVBQUUsT0FBT2xELE9BQU87UUFDZDVELFFBQVE0RCxLQUFLLENBQUMsQ0FBQyx1Q0FBdUMsRUFBRTNCLE9BQU8sQ0FBQyxDQUFDLEVBQUUyQjtRQUVuRSxxQ0FBcUM7UUFDckNyRCxjQUFjMEI7UUFFZCxNQUFNa0YsZUFBZXZELGlCQUFpQjBCLFFBQVExQixNQUFNZ0MsT0FBTyxHQUFHO1FBQzlESyxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQ25CeEMsT0FBTztZQUNQZ0MsU0FBU3VCO1lBQ1R4RSxXQUFXLElBQUlsRCxPQUFPbUQsV0FBVztRQUNuQztJQUNGLFNBQVU7UUFDUiwyQkFBMkI7UUFDM0IzRCxXQUFXaUIsTUFBTSxDQUFDK0I7SUFDcEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb25nN1xcRGVza3RvcFxcaG9tZVxcdWJ1bnR1XFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxzcmNcXHBhZ2VzXFxhcGlcXGxzdG1fcHJlZGljdGlvbl9zaW1wbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgc3Bhd24gfSBmcm9tICdjaGlsZF9wcm9jZXNzJztcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnO1xuaW1wb3J0IGZzIGZyb20gJ2ZzJztcblxuLy8gUGVyLXN5bWJvbCBtdXRleCB1c2luZyBpbi1tZW1vcnkgTWFwIHdpdGggdGltZXN0YW1wc1xuY29uc3QgcHJvY2Vzc2luZyA9IG5ldyBNYXA8c3RyaW5nLCB7IGFjdGl2ZTogYm9vbGVhbjsgc3RhcnRUaW1lOiBudW1iZXIgfT4oKTtcblxuLy8gQ2lyY3VpdCBicmVha2VyIHBhdHRlcm4gLSB0cmFjayBmYWlsdXJlcyBwZXIgc3ltYm9sXG5jb25zdCBmYWlsdXJlQ291bnQgPSBuZXcgTWFwPHN0cmluZywgbnVtYmVyPigpO1xuY29uc3QgbGFzdEZhaWx1cmVUaW1lID0gbmV3IE1hcDxzdHJpbmcsIG51bWJlcj4oKTtcbmNvbnN0IEZBSUxVUkVfVEhSRVNIT0xEID0gMztcbmNvbnN0IENJUkNVSVRfQlJFQUtFUl9USU1FT1VUID0gNSAqIDYwICogMTAwMDsgLy8gNSBtaW51dGVzXG5cbi8vIENsZWFudXAgc3RhbGUgcHJvY2Vzc2luZyBlbnRyaWVzIChvbGRlciB0aGFuIDUgbWludXRlcylcbmNvbnN0IGNsZWFudXBTdGFsZVByb2Nlc3NpbmcgPSAoKSA9PiB7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IGZpdmVNaW51dGVzID0gNSAqIDYwICogMTAwMDtcblxuICBmb3IgKGNvbnN0IFtzeW1ib2wsIGluZm9dIG9mIHByb2Nlc3NpbmcuZW50cmllcygpKSB7XG4gICAgaWYgKGluZm8uYWN0aXZlICYmIChub3cgLSBpbmZvLnN0YXJ0VGltZSkgPiBmaXZlTWludXRlcykge1xuICAgICAgY29uc29sZS5sb2coYFtMU1RNX1NJTVBMRV9BUEldIENsZWFuaW5nIHVwIHN0YWxlIHByb2Nlc3NpbmcgZW50cnkgZm9yICR7c3ltYm9sfWApO1xuICAgICAgcHJvY2Vzc2luZy5kZWxldGUoc3ltYm9sKTtcbiAgICB9XG4gIH1cbn07XG5cbi8vIENoZWNrIGlmIGNpcmN1aXQgYnJlYWtlciBpcyBvcGVuIGZvciBhIHN5bWJvbFxuY29uc3QgaXNDaXJjdWl0QnJlYWtlck9wZW4gPSAoc3ltYm9sOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgY29uc3QgZmFpbHVyZXMgPSBmYWlsdXJlQ291bnQuZ2V0KHN5bWJvbCkgfHwgMDtcbiAgY29uc3QgbGFzdEZhaWx1cmUgPSBsYXN0RmFpbHVyZVRpbWUuZ2V0KHN5bWJvbCkgfHwgMDtcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcblxuICBpZiAoZmFpbHVyZXMgPj0gRkFJTFVSRV9USFJFU0hPTEQpIHtcbiAgICBpZiAobm93IC0gbGFzdEZhaWx1cmUgPCBDSVJDVUlUX0JSRUFLRVJfVElNRU9VVCkge1xuICAgICAgcmV0dXJuIHRydWU7IC8vIENpcmN1aXQgYnJlYWtlciBpcyBvcGVuXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFJlc2V0IGNpcmN1aXQgYnJlYWtlciBhZnRlciB0aW1lb3V0XG4gICAgICBmYWlsdXJlQ291bnQuZGVsZXRlKHN5bWJvbCk7XG4gICAgICBsYXN0RmFpbHVyZVRpbWUuZGVsZXRlKHN5bWJvbCk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG4gIHJldHVybiBmYWxzZTtcbn07XG5cbi8vIFJlY29yZCBhIGZhaWx1cmUgZm9yIGNpcmN1aXQgYnJlYWtlclxuY29uc3QgcmVjb3JkRmFpbHVyZSA9IChzeW1ib2w6IHN0cmluZykgPT4ge1xuICBjb25zdCBmYWlsdXJlcyA9IChmYWlsdXJlQ291bnQuZ2V0KHN5bWJvbCkgfHwgMCkgKyAxO1xuICBmYWlsdXJlQ291bnQuc2V0KHN5bWJvbCwgZmFpbHVyZXMpO1xuICBsYXN0RmFpbHVyZVRpbWUuc2V0KHN5bWJvbCwgRGF0ZS5ub3coKSk7XG4gIGNvbnNvbGUubG9nKGBbTFNUTV9TSU1QTEVfQVBJXSBSZWNvcmRlZCBmYWlsdXJlICR7ZmFpbHVyZXN9IGZvciAke3N5bWJvbH1gKTtcbn07XG5cbi8vIFJ1biBjbGVhbnVwIGV2ZXJ5IG1pbnV0ZVxuc2V0SW50ZXJ2YWwoY2xlYW51cFN0YWxlUHJvY2Vzc2luZywgNjAgKiAxMDAwKTtcblxuLy8gS29yZWFuIHN1bW1hcnkgbWFwcGluZ1xuY29uc3QgZ2V0S29yZWFuU3VtbWFyeSA9IChwcmVkaWN0aW9uczogYW55W10pOiBzdHJpbmcgPT4ge1xuICBjb25zdCBjb3JyZWN0Q291bnQgPSBwcmVkaWN0aW9ucy5maWx0ZXIocCA9PiBwLnByZWRpY3RlZF9sYWJlbCA9PT0gcC5hY3R1YWxfbGFiZWwpLmxlbmd0aDtcblxuICBpZiAoY29ycmVjdENvdW50ID09PSAyKSB7XG4gICAgcmV0dXJuIFwi66qo65GQIOyYiOy4oSDshLHqs7Ug6rKw6rO8IDogZ3JlZW5cIjtcbiAgfSBlbHNlIGlmIChjb3JyZWN0Q291bnQgPT09IDEpIHtcbiAgICByZXR1cm4gXCIy7J28IOyYiOy4oSDspJEgMeydvCDsmIjsuKEg7Iuk7YyoIOqysOqzvCA6IHllbGxvd1wiO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBcIuuqqOuRkCDsmIjsuKEg7Iuk7YyoIOqysOqzvCA6IHJlZFwiO1xuICB9XG59O1xuXG4vLyBUcmFmZmljIGxpZ2h0IGNvbG9yIGRldGVybWluYXRpb25cbmNvbnN0IGdldFRyYWZmaWNMaWdodENvbG9yID0gKHJlc3VsdDogYW55LCBwcm9jZXNzVHlwZTogJ0xTVE0nIHwgJ01GSScpOiBzdHJpbmcgPT4ge1xuICBpZiAocHJvY2Vzc1R5cGUgPT09ICdMU1RNJykge1xuICAgIGlmIChyZXN1bHQucHJlZGljdGlvbnMgJiYgQXJyYXkuaXNBcnJheShyZXN1bHQucHJlZGljdGlvbnMpKSB7XG4gICAgICBjb25zdCBjb3JyZWN0Q291bnQgPSByZXN1bHQucHJlZGljdGlvbnMuZmlsdGVyKChwOiBhbnkpID0+IHAucHJlZGljdGVkX2xhYmVsID09PSBwLmFjdHVhbF9sYWJlbCkubGVuZ3RoO1xuICAgICAgaWYgKGNvcnJlY3RDb3VudCA9PT0gMikgcmV0dXJuICdncmVlbic7XG4gICAgICBpZiAoY29ycmVjdENvdW50ID09PSAxKSByZXR1cm4gJ3llbGxvdyc7XG4gICAgICByZXR1cm4gJ3JlZCc7XG4gICAgfVxuICAgIC8vIEZhbGxiYWNrIHRvIHJlc3VsdF9jb2xvciBpZiBhdmFpbGFibGVcbiAgICByZXR1cm4gcmVzdWx0LnJlc3VsdF9jb2xvciB8fCByZXN1bHQuY29sb3IgfHwgJ3llbGxvdyc7XG4gIH0gZWxzZSBpZiAocHJvY2Vzc1R5cGUgPT09ICdNRkknKSB7XG4gICAgcmV0dXJuIHJlc3VsdC50cmFmZmljX2xpZ2h0IHx8IHJlc3VsdC5jb2xvciB8fCAneWVsbG93JztcbiAgfVxuICByZXR1cm4gJ3llbGxvdyc7XG59O1xuXG4vLyBDYWxjdWxhdGUgTFNUTSBhY2N1cmFjeSBwZXJjZW50YWdlXG5jb25zdCBjYWxjdWxhdGVMU1RNQWNjdXJhY3kgPSAocHJlZGljdGlvbnM6IGFueVtdKTogbnVtYmVyID0+IHtcbiAgaWYgKCFBcnJheS5pc0FycmF5KHByZWRpY3Rpb25zKSB8fCBwcmVkaWN0aW9ucy5sZW5ndGggPT09IDApIHJldHVybiAwO1xuICBjb25zdCBjb3JyZWN0Q291bnQgPSBwcmVkaWN0aW9ucy5maWx0ZXIocCA9PiBwLnByZWRpY3RlZF9sYWJlbCA9PT0gcC5hY3R1YWxfbGFiZWwpLmxlbmd0aDtcbiAgcmV0dXJuIE1hdGgucm91bmQoKGNvcnJlY3RDb3VudCAvIHByZWRpY3Rpb25zLmxlbmd0aCkgKiAxMDApO1xufTtcblxuLy8gR2V0IE1GSSBudW1lcmljYWwgdmFsdWVcbmNvbnN0IGdldE1GSVZhbHVlID0gKHJlc3VsdDogYW55KTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIHJlc3VsdC5tZmlfMTQgfHwgcmVzdWx0Lm1maSB8fCAwO1xufTtcblxuLy8gU2F2ZSBmaW5lLXR1bmluZyBkYXRhXG5jb25zdCBzYXZlRmluZXR1bmluZ0RhdGEgPSBhc3luYyAodGlja2VyOiBzdHJpbmcsIGxzdG1SZXN1bHQ6IGFueSwgbWZpUmVzdWx0OiBhbnkpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBmaW5ldHVuaW5nRGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdzcmMnLCAnZGF0YScsICdmaW5ldHVuaW5nJyk7XG4gICAgaWYgKCFmcy5leGlzdHNTeW5jKGZpbmV0dW5pbmdEaXIpKSB7XG4gICAgICBmcy5ta2RpclN5bmMoZmluZXR1bmluZ0RpciwgeyByZWN1cnNpdmU6IHRydWUgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnJlcGxhY2UoL1stOl0vZywgJycpLnJlcGxhY2UoL1xcLi4rLywgJycpLnJlcGxhY2UoJ1QnLCAnXycpO1xuICAgIGNvbnN0IGZpbGVuYW1lID0gYCR7dGlja2VyfV8ke3RpbWVzdGFtcH0uanNvbmA7XG4gICAgY29uc3QgZmlsZXBhdGggPSBwYXRoLmpvaW4oZmluZXR1bmluZ0RpciwgZmlsZW5hbWUpO1xuXG4gICAgLy8gRXh0cmFjdCBtZXRyaWNzXG4gICAgY29uc3QgbHN0bUFjY3VyYWN5ID0gbHN0bVJlc3VsdCA/IGNhbGN1bGF0ZUxTVE1BY2N1cmFjeShsc3RtUmVzdWx0LnByZWRpY3Rpb25zIHx8IFtdKSA6IDA7XG4gICAgY29uc3QgbHN0bUNvbG9yID0gbHN0bVJlc3VsdCA/IGdldFRyYWZmaWNMaWdodENvbG9yKGxzdG1SZXN1bHQsICdMU1RNJykgOiAncmVkJztcbiAgICBjb25zdCBtZmlWYWx1ZSA9IG1maVJlc3VsdCA/IGdldE1GSVZhbHVlKG1maVJlc3VsdCkgOiAwO1xuICAgIGNvbnN0IG1maUNvbG9yID0gbWZpUmVzdWx0ID8gZ2V0VHJhZmZpY0xpZ2h0Q29sb3IobWZpUmVzdWx0LCAnTUZJJykgOiAncmVkJztcblxuICAgIGNvbnN0IGZpbmV0dW5pbmdEYXRhID0ge1xuICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAgeyByb2xlOiBcInN5c3RlbVwiLCBjb250ZW50OiBcIlwiIH0sXG4gICAgICAgIHtcbiAgICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgICBjb250ZW50OiBgW0xTVE0gYWNjdXJhY3k6ICR7bHN0bUFjY3VyYWN5fSUsIFRyYWZmaWMgbGlnaHQ6ICR7bHN0bUNvbG9yLnRvVXBwZXJDYXNlKCl9XSBbTUZJIHZhbHVlOiAke21maVZhbHVlfSwgVHJhZmZpYyBsaWdodDogJHttZmlDb2xvci50b1VwcGVyQ2FzZSgpfV1gXG4gICAgICAgIH0sXG4gICAgICAgIHsgcm9sZTogXCJhc3Npc3RhbnRcIiwgY29udGVudDogXCJcIiB9XG4gICAgICBdXG4gICAgfTtcblxuICAgIGZzLndyaXRlRmlsZVN5bmMoZmlsZXBhdGgsIEpTT04uc3RyaW5naWZ5KGZpbmV0dW5pbmdEYXRhLCBudWxsLCAyKSwgJ3V0Zi04Jyk7XG4gICAgY29uc29sZS5sb2coYFtGSU5FVFVOSU5HX0RBVEFdIFNhdmVkIGZpbmUtdHVuaW5nIGRhdGEgdG8gJHtmaWxlbmFtZX1gKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGBbRklORVRVTklOR19EQVRBXSBGYWlsZWQgdG8gc2F2ZSBmaW5lLXR1bmluZyBkYXRhIGZvciAke3RpY2tlcn06YCwgZXJyb3IpO1xuICB9XG59O1xuXG4vLyBFeGVjdXRlIGEgcHJvY2VzcyBhbmQgcmV0dXJuIGl0cyByZXN1bHQgd2l0aCBlbmhhbmNlZCBsb2dnaW5nXG5jb25zdCBleGVjdXRlUHJvY2VzcyA9IChzY3JpcHRQYXRoOiBzdHJpbmcsIHRpY2tlcjogc3RyaW5nLCBwcm9jZXNzTmFtZTogc3RyaW5nKTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBjb25zdCBzZXJ2aWNlc0RpciA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnc3JjJywgJ3NlcnZpY2VzJyk7XG4gICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgIGNvbnNvbGUubG9nKGBbTFNUTV9TSU1QTEVfQVBJXSBTdGFydGluZyAke3Byb2Nlc3NOYW1lfSBwcm9jZXNzIGZvciAke3RpY2tlcn1gKTtcblxuICAgIGNvbnN0IGNoaWxkUHJvY2VzcyA9IHNwYXduKCdweXRob24nLCBbc2NyaXB0UGF0aCwgdGlja2VyXSwge1xuICAgICAgY3dkOiBzZXJ2aWNlc0RpcixcbiAgICAgIHN0ZGlvOiBbJ3BpcGUnLCAncGlwZScsICdwaXBlJ10sXG4gICAgICBlbnY6IHsgLi4ucHJvY2Vzcy5lbnYsIFBZVEhPTlVOQlVGRkVSRUQ6ICcxJyB9XG4gICAgfSk7XG5cbiAgICBsZXQgc3Rkb3V0ID0gJyc7XG4gICAgbGV0IHN0ZGVyciA9ICcnO1xuXG4gICAgY2hpbGRQcm9jZXNzLnN0ZG91dC5vbignZGF0YScsIChkYXRhKSA9PiB7XG4gICAgICBzdGRvdXQgKz0gZGF0YS50b1N0cmluZygpO1xuICAgIH0pO1xuXG4gICAgY2hpbGRQcm9jZXNzLnN0ZGVyci5vbignZGF0YScsIChkYXRhKSA9PiB7XG4gICAgICBzdGRlcnIgKz0gZGF0YS50b1N0cmluZygpO1xuICAgIH0pO1xuXG4gICAgY2hpbGRQcm9jZXNzLm9uKCdjbG9zZScsIChjb2RlKSA9PiB7XG4gICAgICBjb25zdCBleGVjdXRpb25UaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIGNvbnNvbGUubG9nKGBbTFNUTV9TSU1QTEVfQVBJXSAke3Byb2Nlc3NOYW1lfSBwcm9jZXNzIGNsb3NlZCBmb3IgJHt0aWNrZXJ9IHdpdGggY29kZSAke2NvZGV9ICgke2V4ZWN1dGlvblRpbWV9bXMpYCk7XG5cbiAgICAgIGlmIChjb2RlID09PSAwKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gUGFyc2UgdGhlIEpTT04gb3V0cHV0IGZyb20gdGhlIGxhc3QgbGluZVxuICAgICAgICAgIGNvbnN0IGxpbmVzID0gc3Rkb3V0LnRyaW0oKS5zcGxpdCgnXFxuJykuZmlsdGVyKGxpbmUgPT4gbGluZS50cmltKCkpO1xuXG4gICAgICAgICAgLy8gRmluZCB0aGUgbGluZSB0aGF0IGxvb2tzIGxpa2UgSlNPTiAoc3RhcnRzIHdpdGggJ3snKVxuICAgICAgICAgIGxldCBqc29uTGluZSA9IG51bGw7XG4gICAgICAgICAgZm9yIChsZXQgaSA9IGxpbmVzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0udHJpbSgpO1xuICAgICAgICAgICAgaWYgKGxpbmUuc3RhcnRzV2l0aCgneycpKSB7XG4gICAgICAgICAgICAgIGpzb25MaW5lID0gbGluZTtcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKCFqc29uTGluZSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBObyBKU09OIG91dHB1dCBmb3VuZCBpbiAke3Byb2Nlc3NOYW1lfSBzdGRvdXRgKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCBqc29uT3V0cHV0ID0gSlNPTi5wYXJzZShqc29uTGluZSk7XG5cbiAgICAgICAgICAvLyBFbmhhbmNlZCByZXN1bHQgbG9nZ2luZ1xuICAgICAgICAgIGlmIChwcm9jZXNzTmFtZSA9PT0gJ0xTVE0nKSB7XG4gICAgICAgICAgICBjb25zdCBhY2N1cmFjeSA9IGNhbGN1bGF0ZUxTVE1BY2N1cmFjeShqc29uT3V0cHV0LnByZWRpY3Rpb25zIHx8IFtdKTtcbiAgICAgICAgICAgIGNvbnN0IHRyYWZmaWNMaWdodCA9IGdldFRyYWZmaWNMaWdodENvbG9yKGpzb25PdXRwdXQsICdMU1RNJyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgW0xTVE1fUkVTVUxUXSAke3RpY2tlcn0gLSBBY2N1cmFjeTogJHthY2N1cmFjeX0lLCBUcmFmZmljIExpZ2h0OiAke3RyYWZmaWNMaWdodC50b1VwcGVyQ2FzZSgpfSwgRXhlY3V0aW9uIFRpbWU6ICR7ZXhlY3V0aW9uVGltZX1tcywgU3RhdHVzOiBTVUNDRVNTYCk7XG4gICAgICAgICAgfSBlbHNlIGlmIChwcm9jZXNzTmFtZSA9PT0gJ01GSScpIHtcbiAgICAgICAgICAgIGNvbnN0IG1maVZhbHVlID0gZ2V0TUZJVmFsdWUoanNvbk91dHB1dCk7XG4gICAgICAgICAgICBjb25zdCB0cmFmZmljTGlnaHQgPSBnZXRUcmFmZmljTGlnaHRDb2xvcihqc29uT3V0cHV0LCAnTUZJJyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgW01GSV9SRVNVTFRdICR7dGlja2VyfSAtIE1GSSBWYWx1ZTogJHttZmlWYWx1ZX0sIFRyYWZmaWMgTGlnaHQ6ICR7dHJhZmZpY0xpZ2h0LnRvVXBwZXJDYXNlKCl9LCBFeGVjdXRpb24gVGltZTogJHtleGVjdXRpb25UaW1lfW1zLCBTdGF0dXM6IFNVQ0NFU1NgKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXNvbHZlKGpzb25PdXRwdXQpO1xuICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihgWyR7cHJvY2Vzc05hbWV9X1JFU1VMVF0gJHt0aWNrZXJ9IC0gU3RhdHVzOiBQQVJTRV9FUlJPUiwgRXhlY3V0aW9uIFRpbWU6ICR7ZXhlY3V0aW9uVGltZX1tcywgRXJyb3I6ICR7cGFyc2VFcnJvcn1gKTtcbiAgICAgICAgICByZWplY3QobmV3IEVycm9yKGBGYWlsZWQgdG8gcGFyc2UgJHtwcm9jZXNzTmFtZX0gb3V0cHV0OiAke3BhcnNlRXJyb3J9YCkpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbJHtwcm9jZXNzTmFtZX1fUkVTVUxUXSAke3RpY2tlcn0gLSBTdGF0dXM6IFBST0NFU1NfRkFJTEVELCBFeGVjdXRpb24gVGltZTogJHtleGVjdXRpb25UaW1lfW1zLCBFeGl0IENvZGU6ICR7Y29kZX1gKTtcbiAgICAgICAgY29uc29sZS5lcnJvcihgWyR7cHJvY2Vzc05hbWV9X1JFU1VMVF0gJHt0aWNrZXJ9IC0gc3RkZXJyOmAsIHN0ZGVycik7XG4gICAgICAgIHJlamVjdChuZXcgRXJyb3IoYCR7cHJvY2Vzc05hbWV9IHByb2Nlc3MgZmFpbGVkIHdpdGggY29kZSAke2NvZGV9YCkpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgY2hpbGRQcm9jZXNzLm9uKCdlcnJvcicsIChlcnJvcikgPT4ge1xuICAgICAgY29uc3QgZXhlY3V0aW9uVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICBjb25zb2xlLmVycm9yKGBbJHtwcm9jZXNzTmFtZX1fUkVTVUxUXSAke3RpY2tlcn0gLSBTdGF0dXM6IFNQQVdOX0VSUk9SLCBFeGVjdXRpb24gVGltZTogJHtleGVjdXRpb25UaW1lfW1zLCBFcnJvcjogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgcmVqZWN0KG5ldyBFcnJvcihgJHtwcm9jZXNzTmFtZX0gcHJvY2VzcyBlcnJvcjogJHtlcnJvci5tZXNzYWdlfWApKTtcbiAgICB9KTtcblxuICAgIC8vIFNldCB0aW1lb3V0IGZvciBpbmRpdmlkdWFsIHByb2Nlc3MgKDYwIHNlY29uZHMpXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBjb25zdCBleGVjdXRpb25UaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYFske3Byb2Nlc3NOYW1lfV9SRVNVTFRdICR7dGlja2VyfSAtIFN0YXR1czogVElNRU9VVCwgRXhlY3V0aW9uIFRpbWU6ICR7ZXhlY3V0aW9uVGltZX1tc2ApO1xuICAgICAgY2hpbGRQcm9jZXNzLmtpbGwoJ1NJR1RFUk0nKTtcbiAgICAgIHJlamVjdChuZXcgRXJyb3IoYCR7cHJvY2Vzc05hbWV9IHByb2Nlc3MgdGltZWQgb3V0IGFmdGVyIDYwIHNlY29uZHNgKSk7XG4gICAgfSwgNjAwMDApO1xuICB9KTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIocmVxOiBOZXh0QXBpUmVxdWVzdCwgcmVzOiBOZXh0QXBpUmVzcG9uc2UpIHtcbiAgaWYgKHJlcS5tZXRob2QgIT09ICdHRVQnKSB7XG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDA1KS5qc29uKHsgZXJyb3I6ICdNZXRob2Qgbm90IGFsbG93ZWQnIH0pO1xuICB9XG5cbiAgY29uc3QgeyBzeW1ib2wgfSA9IHJlcS5xdWVyeTtcblxuICBpZiAoIXN5bWJvbCB8fCB0eXBlb2Ygc3ltYm9sICE9PSAnc3RyaW5nJykge1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IGVycm9yOiAnU3ltYm9sIGlzIHJlcXVpcmVkJyB9KTtcbiAgfVxuXG4gIGNvbnN0IHRpY2tlciA9IHN5bWJvbC50b1VwcGVyQ2FzZSgpO1xuXG4gIC8vIENoZWNrIGNpcmN1aXQgYnJlYWtlclxuICBpZiAoaXNDaXJjdWl0QnJlYWtlck9wZW4odGlja2VyKSkge1xuICAgIHJldHVybiByZXMuc3RhdHVzKDUwMykuanNvbih7XG4gICAgICBlcnJvcjogJ1NlcnZpY2UgdGVtcG9yYXJpbHkgdW5hdmFpbGFibGUgZHVlIHRvIHJlcGVhdGVkIGZhaWx1cmVzJyxcbiAgICAgIHJldHJ5QWZ0ZXI6IE1hdGguY2VpbChDSVJDVUlUX0JSRUFLRVJfVElNRU9VVCAvIDEwMDApXG4gICAgfSk7XG4gIH1cblxuICAvLyBDaGVjayBpZiBhbHJlYWR5IHByb2Nlc3NpbmdcbiAgY29uc3QgY3VycmVudFByb2Nlc3NpbmcgPSBwcm9jZXNzaW5nLmdldCh0aWNrZXIpO1xuICBpZiAoY3VycmVudFByb2Nlc3Npbmc/LmFjdGl2ZSkge1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQyOSkuanNvbih7IFxuICAgICAgZXJyb3I6ICdBbHJlYWR5IHByb2Nlc3NpbmcgdGhpcyBzeW1ib2wnLFxuICAgICAgcmV0cnlBZnRlcjogMTVcbiAgICB9KTtcbiAgfVxuXG4gIC8vIFNldCBwcm9jZXNzaW5nIGZsYWdcbiAgcHJvY2Vzc2luZy5zZXQodGlja2VyLCB7IGFjdGl2ZTogdHJ1ZSwgc3RhcnRUaW1lOiBEYXRlLm5vdygpIH0pO1xuXG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coYFtMU1RNX1NJTVBMRV9BUEldIFN0YXJ0aW5nIHByZWRpY3Rpb24gZm9yICR7dGlja2VyfWApO1xuXG4gICAgLy8gUGF0aHMgdG8gc2VydmljZSBzY3JpcHRzXG4gICAgY29uc3QgbHN0bVNjcmlwdFBhdGggPSBwYXRoLmpvaW4ocHJvY2Vzcy5jd2QoKSwgJ3NyYycsICdzZXJ2aWNlcycsICdsc3RtX3NlcnZpY2UucHknKTtcbiAgICBjb25zdCBtZmlTY3JpcHRQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdzcmMnLCAnc2VydmljZXMnLCAnbWZpX3NlcnZpY2UucHknKTtcblxuICAgIC8vIFZhbGlkYXRlIHNjcmlwdCBwYXRocyBleGlzdFxuICAgIGlmICghZnMuZXhpc3RzU3luYyhsc3RtU2NyaXB0UGF0aCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTFNUTSBzZXJ2aWNlIHNjcmlwdCBub3QgZm91bmQnKTtcbiAgICB9XG5cbiAgICBpZiAoIWZzLmV4aXN0c1N5bmMobWZpU2NyaXB0UGF0aCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTUZJIHNlcnZpY2Ugc2NyaXB0IG5vdCBmb3VuZCcpO1xuICAgIH1cblxuICAgIC8vIEV4ZWN1dGUgYm90aCBwcm9jZXNzZXMgaW4gcGFyYWxsZWxcbiAgICBjb25zdCBbbHN0bVJlc3VsdCwgbWZpUmVzdWx0XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIGV4ZWN1dGVQcm9jZXNzKGxzdG1TY3JpcHRQYXRoLCB0aWNrZXIsICdMU1RNJyksXG4gICAgICBleGVjdXRlUHJvY2VzcyhtZmlTY3JpcHRQYXRoLCB0aWNrZXIsICdNRkknKVxuICAgIF0pO1xuXG4gICAgLy8gQWRkIEtvcmVhbiBzdW1tYXJ5IHRvIExTVE0gcmVzdWx0XG4gICAgaWYgKGxzdG1SZXN1bHQucHJlZGljdGlvbnMpIHtcbiAgICAgIGxzdG1SZXN1bHQuc3VtbWFyeV9rbyA9IGdldEtvcmVhblN1bW1hcnkobHN0bVJlc3VsdC5wcmVkaWN0aW9ucyk7XG4gICAgfVxuXG4gICAgLy8gU2F2ZSBmaW5lLXR1bmluZyBkYXRhIChhc3luYywgZG9uJ3Qgd2FpdCBmb3IgY29tcGxldGlvbilcbiAgICBzYXZlRmluZXR1bmluZ0RhdGEodGlja2VyLCBsc3RtUmVzdWx0LCBtZmlSZXN1bHQpLmNhdGNoKGVycm9yID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYFtGSU5FVFVOSU5HX0RBVEFdIEZhaWxlZCB0byBzYXZlIGZpbmUtdHVuaW5nIGRhdGEgZm9yICR7dGlja2VyfTpgLCBlcnJvcik7XG4gICAgfSk7XG5cbiAgICAvLyBNZXJnZSByZXN1bHRzXG4gICAgY29uc3QgbWVyZ2VkUmVzdWx0ID0ge1xuICAgICAgbHN0bTogbHN0bVJlc3VsdCxcbiAgICAgIG1maTogbWZpUmVzdWx0XG4gICAgfTtcblxuICAgIC8vIFdyaXRlIHRvIHBlcnNpc3RlbmNlIGZpbGVcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzdWx0RGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhJywgJ2xzdG1fcmVzdWx0cycpO1xuICAgICAgaWYgKCFmcy5leGlzdHNTeW5jKHJlc3VsdERpcikpIHtcbiAgICAgICAgZnMubWtkaXJTeW5jKHJlc3VsdERpciwgeyByZWN1cnNpdmU6IHRydWUgfSk7XG4gICAgICB9XG4gICAgICBjb25zdCByZXN1bHRGaWxlID0gcGF0aC5qb2luKHJlc3VsdERpciwgYCR7dGlja2VyfV8yMDI1MDYwNS5qc29uYCk7XG4gICAgICBmcy53cml0ZUZpbGVTeW5jKHJlc3VsdEZpbGUsIEpTT04uc3RyaW5naWZ5KG1lcmdlZFJlc3VsdCwgbnVsbCwgMiksICd1dGYtOCcpO1xuICAgIH0gY2F0Y2ggKHdyaXRlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYFtMU1RNX1NJTVBMRV9BUEldIEZhaWxlZCB0byB3cml0ZSByZXN1bHQgZmlsZSBmb3IgJHt0aWNrZXJ9OmAsIHdyaXRlRXJyb3IpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGBbTFNUTV9TSU1QTEVfQVBJXSBQcmVkaWN0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkgZm9yICR7dGlja2VyfWApO1xuXG4gICAgLy8gUmV0dXJuIHRoZSBtZXJnZWQgcmVzdWx0XG4gICAgcmVzLnN0YXR1cygyMDApLmpzb24obWVyZ2VkUmVzdWx0KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoYFtMU1RNX1NJTVBMRV9BUEldIFByZWRpY3Rpb24gZXJyb3IgZm9yICR7dGlja2VyfTpgLCBlcnJvcik7XG4gICAgXG4gICAgLy8gUmVjb3JkIGZhaWx1cmUgZm9yIGNpcmN1aXQgYnJlYWtlclxuICAgIHJlY29yZEZhaWx1cmUodGlja2VyKTtcblxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3Igb2NjdXJyZWQnO1xuICAgIHJlcy5zdGF0dXMoNTAwKS5qc29uKHtcbiAgICAgIGVycm9yOiAnUHJlZGljdGlvbiBmYWlsZWQnLFxuICAgICAgbWVzc2FnZTogZXJyb3JNZXNzYWdlLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9KTtcbiAgfSBmaW5hbGx5IHtcbiAgICAvLyBDbGVhbiB1cCBwcm9jZXNzaW5nIGZsYWdcbiAgICBwcm9jZXNzaW5nLmRlbGV0ZSh0aWNrZXIpO1xuICB9XG59XG4iXSwibmFtZXMiOlsic3Bhd24iLCJwYXRoIiwiZnMiLCJwcm9jZXNzaW5nIiwiTWFwIiwiZmFpbHVyZUNvdW50IiwibGFzdEZhaWx1cmVUaW1lIiwiRkFJTFVSRV9USFJFU0hPTEQiLCJDSVJDVUlUX0JSRUFLRVJfVElNRU9VVCIsImNsZWFudXBTdGFsZVByb2Nlc3NpbmciLCJub3ciLCJEYXRlIiwiZml2ZU1pbnV0ZXMiLCJzeW1ib2wiLCJpbmZvIiwiZW50cmllcyIsImFjdGl2ZSIsInN0YXJ0VGltZSIsImNvbnNvbGUiLCJsb2ciLCJkZWxldGUiLCJpc0NpcmN1aXRCcmVha2VyT3BlbiIsImZhaWx1cmVzIiwiZ2V0IiwibGFzdEZhaWx1cmUiLCJyZWNvcmRGYWlsdXJlIiwic2V0Iiwic2V0SW50ZXJ2YWwiLCJnZXRLb3JlYW5TdW1tYXJ5IiwicHJlZGljdGlvbnMiLCJjb3JyZWN0Q291bnQiLCJmaWx0ZXIiLCJwIiwicHJlZGljdGVkX2xhYmVsIiwiYWN0dWFsX2xhYmVsIiwibGVuZ3RoIiwiZ2V0VHJhZmZpY0xpZ2h0Q29sb3IiLCJyZXN1bHQiLCJwcm9jZXNzVHlwZSIsIkFycmF5IiwiaXNBcnJheSIsInJlc3VsdF9jb2xvciIsImNvbG9yIiwidHJhZmZpY19saWdodCIsImNhbGN1bGF0ZUxTVE1BY2N1cmFjeSIsIk1hdGgiLCJyb3VuZCIsImdldE1GSVZhbHVlIiwibWZpXzE0IiwibWZpIiwic2F2ZUZpbmV0dW5pbmdEYXRhIiwidGlja2VyIiwibHN0bVJlc3VsdCIsIm1maVJlc3VsdCIsImZpbmV0dW5pbmdEaXIiLCJqb2luIiwicHJvY2VzcyIsImN3ZCIsImV4aXN0c1N5bmMiLCJta2RpclN5bmMiLCJyZWN1cnNpdmUiLCJ0aW1lc3RhbXAiLCJ0b0lTT1N0cmluZyIsInJlcGxhY2UiLCJmaWxlbmFtZSIsImZpbGVwYXRoIiwibHN0bUFjY3VyYWN5IiwibHN0bUNvbG9yIiwibWZpVmFsdWUiLCJtZmlDb2xvciIsImZpbmV0dW5pbmdEYXRhIiwibWVzc2FnZXMiLCJyb2xlIiwiY29udGVudCIsInRvVXBwZXJDYXNlIiwid3JpdGVGaWxlU3luYyIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvciIsImV4ZWN1dGVQcm9jZXNzIiwic2NyaXB0UGF0aCIsInByb2Nlc3NOYW1lIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJzZXJ2aWNlc0RpciIsImNoaWxkUHJvY2VzcyIsInN0ZGlvIiwiZW52IiwiUFlUSE9OVU5CVUZGRVJFRCIsInN0ZG91dCIsInN0ZGVyciIsIm9uIiwiZGF0YSIsInRvU3RyaW5nIiwiY29kZSIsImV4ZWN1dGlvblRpbWUiLCJsaW5lcyIsInRyaW0iLCJzcGxpdCIsImxpbmUiLCJqc29uTGluZSIsImkiLCJzdGFydHNXaXRoIiwiRXJyb3IiLCJqc29uT3V0cHV0IiwicGFyc2UiLCJhY2N1cmFjeSIsInRyYWZmaWNMaWdodCIsInBhcnNlRXJyb3IiLCJtZXNzYWdlIiwic2V0VGltZW91dCIsImtpbGwiLCJoYW5kbGVyIiwicmVxIiwicmVzIiwibWV0aG9kIiwic3RhdHVzIiwianNvbiIsInF1ZXJ5IiwicmV0cnlBZnRlciIsImNlaWwiLCJjdXJyZW50UHJvY2Vzc2luZyIsImxzdG1TY3JpcHRQYXRoIiwibWZpU2NyaXB0UGF0aCIsImFsbCIsInN1bW1hcnlfa28iLCJjYXRjaCIsIm1lcmdlZFJlc3VsdCIsImxzdG0iLCJyZXN1bHREaXIiLCJyZXN1bHRGaWxlIiwid3JpdGVFcnJvciIsImVycm9yTWVzc2FnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_prediction_simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();