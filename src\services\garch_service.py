# src/services/gjr_var_service.py
"""
GJR-GARCH(1,1,1) 기반 1-일 ahead 조건부 VaR(95 %) 계산 → 신호등 JSON 반환
필수 파일 두 개는 모두 C:/.../financial_dashboard/src/data 에 존재해야 함
    1) sp500_adj_close_3y.csv      (Date, 티커별 Adj Close)
    2) sp500_enriched_final.ts     (티커 ↔ 산업; 여기선 사용 X)
"""

import sys, json
from pathlib import Path
import pandas as pd
from arch import arch_model

# ── 경로 상수 ──────────────────────────────────────────────────────────
BASE_DIR   = Path(__file__).resolve().parent          # …/src/services
DATA_DIR   = BASE_DIR.parent / "data"                 # …/src/data
PRICE_FILE = DATA_DIR / "sp500_adj_close_3y.csv"

# ── VaR 계산 함수 ─────────────────────────────────────────────────────
def gjr_var(ticker: str, alpha: float = 0.05):
    """티커별 GJR-GARCH(1,1,1) → 1-일 ahead VaR α(예: 5 %)"""
    if not PRICE_FILE.exists():
        raise FileNotFoundError(f"가격 CSV를 찾을 수 없습니다 → {PRICE_FILE}")

    price = pd.read_csv(PRICE_FILE, parse_dates=["Date"], index_col="Date")
    if ticker not in price.columns:
        raise KeyError(f"{ticker} 열이 CSV에 없습니다.")
    close = price[ticker].dropna()
    ret = close.pct_change().dropna() * 100            # % 단위

    model = arch_model(ret, p=1, o=1, q=1, dist="t")
    fit   = model.fit(update_freq=0, disp="off")       # GJR(1,1,1)
    sigma = (fit.forecast(horizon=1).variance.iloc[-1, 0] ** 0.5) / 100
    var95 = 1.65 * sigma                               # 정규 근사 95 %
    var_pct = var95 / close.iloc[-1]

    if var_pct > 0.03:
        light = "red"
    elif var_pct > 0.02:
        light = "yellow"
    else:
        light = "green"

    return {
        "symbol": ticker,
        "date": str(close.index[-1].date()),
        "sigma": round(float(sigma), 4),
        "var_pct": round(float(var_pct), 3),
        "traffic_light": light
    }

# ── 실행부 ────────────────────────────────────────────────────────────
if __name__ == "__main__":
    if len(sys.argv) != 2:
        sys.exit("Usage: python gjr_var_service.py <TICKER>")
    try:
        print(json.dumps(gjr_var(sys.argv[1].upper()), ensure_ascii=False))
    except Exception:
        import traceback, sys as _s
        traceback.print_exc()
        _s.exit(1)
