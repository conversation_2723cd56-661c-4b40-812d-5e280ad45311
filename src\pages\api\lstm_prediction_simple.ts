import { NextApiRequest, NextApiResponse } from 'next';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

// Per-symbol mutex using in-memory Map with timestamps
const processing = new Map<string, { active: boolean; startTime: number }>();

// Circuit breaker pattern - track failures per symbol
const failureCount = new Map<string, number>();
const lastFailureTime = new Map<string, number>();
const FAILURE_THRESHOLD = 3;
const CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes

// Cleanup stale processing entries (older than 5 minutes)
const cleanupStaleProcessing = () => {
  const now = Date.now();
  const fiveMinutes = 5 * 60 * 1000;

  for (const [symbol, info] of processing.entries()) {
    if (info.active && (now - info.startTime) > fiveMinutes) {
      console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);
      processing.delete(symbol);
    }
  }
};

// Check if circuit breaker is open for a symbol
const isCircuitBreakerOpen = (symbol: string): boolean => {
  const failures = failureCount.get(symbol) || 0;
  const lastFailure = lastFailureTime.get(symbol) || 0;
  const now = Date.now();

  if (failures >= FAILURE_THRESHOLD) {
    if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {
      return true; // Circuit breaker is open
    } else {
      // Reset circuit breaker after timeout
      failureCount.delete(symbol);
      lastFailureTime.delete(symbol);
      return false;
    }
  }
  return false;
};

// Record a failure for circuit breaker
const recordFailure = (symbol: string) => {
  const failures = (failureCount.get(symbol) || 0) + 1;
  failureCount.set(symbol, failures);
  lastFailureTime.set(symbol, Date.now());
  console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);
};

// Run cleanup every minute
setInterval(cleanupStaleProcessing, 60 * 1000);

// Korean summary mapping
const getKoreanSummary = (predictions: any[]): string => {
  const correctCount = predictions.filter(p => p.predicted_label === p.actual_label).length;

  if (correctCount === 2) {
    return "모두 예측 성공 결과 : green";
  } else if (correctCount === 1) {
    return "2일 예측 중 1일 예측 실패 결과 : yellow";
  } else {
    return "모두 예측 실패 결과 : red";
  }
};

// Traffic light color determination
const getTrafficLightColor = (result: any, processType: 'LSTM' | 'MFI'): string => {
  if (processType === 'LSTM') {
    if (result.predictions && Array.isArray(result.predictions)) {
      const correctCount = result.predictions.filter((p: any) => p.predicted_label === p.actual_label).length;
      if (correctCount === 2) return 'green';
      if (correctCount === 1) return 'yellow';
      return 'red';
    }
    // Fallback to result_color if available
    return result.result_color || result.color || 'yellow';
  } else if (processType === 'MFI') {
    return result.traffic_light || result.color || 'yellow';
  }
  return 'yellow';
};

// Calculate LSTM accuracy percentage
const calculateLSTMAccuracy = (predictions: any[]): number => {
  if (!Array.isArray(predictions) || predictions.length === 0) return 0;
  const correctCount = predictions.filter(p => p.predicted_label === p.actual_label).length;
  return Math.round((correctCount / predictions.length) * 100);
};

// Get LSTM pred_prob_up value
const getLSTMPredProbUp = (result: any): number => {
  if (result.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {
    return result.predictions[0].pred_prob_up || 0;
  }
  return result.pred_prob_up || 0;
};

// Get MFI numerical value
const getMFIValue = (result: any): number => {
  return result.mfi_14 || result.mfi || 0;
};

// Get service-specific values for logging and fine-tuning
const getBollingerValue = (result: any): number => {
  return result.bollinger_position || result.position || 0;
};

const getRSIValue = (result: any): number => {
  return result.rsi_14 || result.rsi || 0;
};

const getIndustryValue = (result: any): string => {
  return result.industry_sentiment || result.sentiment || 'neutral';
};

const getGARCHValue = (result: any): number => {
  return result.volatility_forecast || result.volatility || 0;
};

// Majority vote logic for technical analysis (MFI, Bollinger, RSI)
const getTechnicalMajorityVote = (mfiColor: string, bollingerColor: string, rsiColor: string): string => {
  const colors = [mfiColor, bollingerColor, rsiColor];
  const colorCounts = {
    green: colors.filter(c => c === 'green').length,
    yellow: colors.filter(c => c === 'yellow').length,
    red: colors.filter(c => c === 'red').length
  };

  // Return the color with the highest count
  if (colorCounts.green >= 2) return 'green';
  if (colorCounts.red >= 2) return 'red';
  return 'yellow'; // Default to yellow if no majority or all different
};

// Save fine-tuning data
const saveFinetuningData = async (ticker: string, lstmResult: any, mfiResult: any): Promise<void> => {
  try {
    const finetuningDir = path.join(process.cwd(), 'src', 'data', 'finetuning');
    if (!fs.existsSync(finetuningDir)) {
      fs.mkdirSync(finetuningDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '').replace('T', '_');
    const filename = `${ticker}_${timestamp}.json`;
    const filepath = path.join(finetuningDir, filename);

    // Extract metrics
    const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;
    const lstmColor = lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red';
    const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;
    const mfiColor = mfiResult ? getTrafficLightColor(mfiResult, 'MFI') : 'red';

    const finetuningData = {
      messages: [
        { role: "system", content: "" },
        {
          role: "user",
          content: `[LSTM accuracy: ${lstmAccuracy}%, Traffic light: ${lstmColor.toUpperCase()}] [MFI value: ${mfiValue}, Traffic light: ${mfiColor.toUpperCase()}]`
        },
        { role: "assistant", content: "" }
      ]
    };

    fs.writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');
    console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);
  } catch (error) {
    console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);
  }
};

// Execute a process and return its result with enhanced logging
const executeProcess = (scriptPath: string, ticker: string, processName: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const servicesDir = path.join(process.cwd(), 'src', 'services');
    const startTime = Date.now();

    console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);

    const childProcess = spawn('python', [scriptPath, ticker], {
      cwd: servicesDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, PYTHONUNBUFFERED: '1' }
    });

    let stdout = '';
    let stderr = '';

    childProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    childProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    childProcess.on('close', (code) => {
      const executionTime = Date.now() - startTime;
      console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);

      if (code === 0) {
        try {
          // Parse the JSON output from the last line
          const lines = stdout.trim().split('\n').filter(line => line.trim());

          // Find the line that looks like JSON (starts with '{')
          let jsonLine = null;
          for (let i = lines.length - 1; i >= 0; i--) {
            const line = lines[i].trim();
            if (line.startsWith('{')) {
              jsonLine = line;
              break;
            }
          }

          if (!jsonLine) {
            throw new Error(`No JSON output found in ${processName} stdout`);
          }

          const jsonOutput = JSON.parse(jsonLine);

          // Enhanced result logging
          if (processName === 'LSTM') {
            const accuracy = calculateLSTMAccuracy(jsonOutput.predictions || []);
            const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');
            console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);
          } else if (processName === 'MFI') {
            const mfiValue = getMFIValue(jsonOutput);
            const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');
            console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);
          }

          resolve(jsonOutput);
        } catch (parseError) {
          console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);
          reject(new Error(`Failed to parse ${processName} output: ${parseError}`));
        }
      } else {
        console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);
        console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);
        reject(new Error(`${processName} process failed with code ${code}`));
      }
    });

    childProcess.on('error', (error) => {
      const executionTime = Date.now() - startTime;
      console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);
      reject(new Error(`${processName} process error: ${error.message}`));
    });

    // Set timeout for individual process (60 seconds)
    setTimeout(() => {
      const executionTime = Date.now() - startTime;
      console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);
      childProcess.kill('SIGTERM');
      reject(new Error(`${processName} process timed out after 60 seconds`));
    }, 60000);
  });
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { symbol } = req.query;

  if (!symbol || typeof symbol !== 'string') {
    return res.status(400).json({ error: 'Symbol is required' });
  }

  const ticker = symbol.toUpperCase();

  // Check circuit breaker
  if (isCircuitBreakerOpen(ticker)) {
    return res.status(503).json({
      error: 'Service temporarily unavailable due to repeated failures',
      retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)
    });
  }

  // Check if already processing
  const currentProcessing = processing.get(ticker);
  if (currentProcessing?.active) {
    return res.status(429).json({ 
      error: 'Already processing this symbol',
      retryAfter: 15
    });
  }

  // Set processing flag
  processing.set(ticker, { active: true, startTime: Date.now() });

  try {
    console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);

    // Paths to service scripts
    const lstmScriptPath = path.join(process.cwd(), 'src', 'services', 'lstm_service.py');
    const mfiScriptPath = path.join(process.cwd(), 'src', 'services', 'mfi_service.py');

    // Validate script paths exist
    if (!fs.existsSync(lstmScriptPath)) {
      throw new Error('LSTM service script not found');
    }

    if (!fs.existsSync(mfiScriptPath)) {
      throw new Error('MFI service script not found');
    }

    // Execute both processes in parallel
    const [lstmResult, mfiResult] = await Promise.all([
      executeProcess(lstmScriptPath, ticker, 'LSTM'),
      executeProcess(mfiScriptPath, ticker, 'MFI')
    ]);

    // Add Korean summary to LSTM result
    if (lstmResult.predictions) {
      lstmResult.summary_ko = getKoreanSummary(lstmResult.predictions);
    }

    // Save fine-tuning data (async, don't wait for completion)
    saveFinetuningData(ticker, lstmResult, mfiResult).catch(error => {
      console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);
    });

    // Merge results
    const mergedResult = {
      lstm: lstmResult,
      mfi: mfiResult
    };

    // Write to persistence file
    try {
      const resultDir = path.join(process.cwd(), 'data', 'lstm_results');
      if (!fs.existsSync(resultDir)) {
        fs.mkdirSync(resultDir, { recursive: true });
      }
      const resultFile = path.join(resultDir, `${ticker}_20250605.json`);
      fs.writeFileSync(resultFile, JSON.stringify(mergedResult, null, 2), 'utf-8');
    } catch (writeError) {
      console.error(`[LSTM_SIMPLE_API] Failed to write result file for ${ticker}:`, writeError);
    }

    console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker}`);

    // Return the merged result
    res.status(200).json(mergedResult);

  } catch (error) {
    console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);
    
    // Record failure for circuit breaker
    recordFailure(ticker);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({
      error: 'Prediction failed',
      message: errorMessage,
      timestamp: new Date().toISOString()
    });
  } finally {
    // Clean up processing flag
    processing.delete(ticker);
  }
}
