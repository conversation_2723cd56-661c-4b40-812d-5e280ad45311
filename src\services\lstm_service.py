#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
Predicts next-day stock price direction for specific dates
"""

import os
import sys
import json
import logging
from datetime import date
from pathlib import Path

# Module-level constants
TRAIN_END = date(2025, 6, 3)
PRED_DATES = [date(2025, 6, 4), date(2025, 6, 5)]
RESULT_DIR = Path(__file__).resolve().parents[1] / "data" / "lstm_results"

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.regularizers import l2
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import AdamW

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def load_and_prepare_data(ticker, use_volume=False):
    """Load data and prepare for training"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]              # …/financial_dashboard
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)

        # Parse Date column
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Load volume data if requested
        volume_data = True 
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if not volume_path.exists():
                print(f"Volume data file not found: {volume_path}", file=sys.stderr)
                sys.exit(1)

            volume_df = pd.read_csv(volume_path)
            volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

            # Convert numeric columns to float64
            for col in volume_df.columns:
                if col != 'Date':
                    volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

            if ticker not in volume_df.columns:
                print(f"Ticker {ticker} not found in volume data", file=sys.stderr)
                sys.exit(1)

            volume_data = volume_df[['Date', ticker]].copy()
            volume_data.columns = ['Date', 'Volume']

        # Filter training data (Date <= TRAIN_END)
        train_data = df[df['Date'] <= TRAIN_END].copy()

        # Check minimum rows requirement
        if len(train_data) < 60:
            print(f"Insufficient training data: {len(train_data)} rows (minimum 60 required)", file=sys.stderr)
            sys.exit(1)

        # Get all data for predictions
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[volume_data['Date'] <= TRAIN_END].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'})

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(data, sequence_length=60, use_volume=False):
    """Create sequences for LSTM training"""
    X, y = [], []

    if use_volume:
        # data should be 2D array with [price, volume] columns
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            # Binary target based on price (first column): 1 if price increased, 0 if decreased
            y.append(1 if data[i, 0] > data[i-1, 0] else 0)
    else:
        # data is 1D array with price only
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            # Binary target: 1 if price increased, 0 if decreased
            y.append(1 if data[i] > data[i-1] else 0)

    return np.array(X), np.array(y)


def build_lstm_model(input_shape):
    """Build Intel-optimized LSTM classifier model with L2 regularization"""
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.1),

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss='binary_crossentropy',
        metrics=['binary_accuracy']
    )

    return model


def predict_for_dates(model, scaler, all_data, pred_dates, use_volume=False):
    """Make predictions for specific dates"""
    predictions = []

    for pred_date in pred_dates:
        # Find the date in data
        date_mask = all_data['Date'] == pred_date
        if not date_mask.any():
            print(f"Date {pred_date} not found in data", file=sys.stderr)
            sys.exit(1)

        date_idx = all_data[date_mask].index[0]

        # Get 60 days before this date for prediction
        if date_idx < 60:
            print(f"Insufficient data before {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Get sequence data
        if use_volume:
            # Get both price and volume data
            sequence_data = all_data.iloc[date_idx-60:date_idx][['Adj Close', 'Volume']].values
            sequence_scaled = scaler.transform(sequence_data)
            sequence_scaled = sequence_scaled.reshape(1, 60, 2)
        else:
            # Get price data only
            sequence_data = all_data.iloc[date_idx-60:date_idx]['Adj Close'].values
            sequence_scaled = scaler.transform(sequence_data.reshape(-1, 1))
            sequence_scaled = sequence_scaled.reshape(1, 60, 1)

        # Make prediction
        pred_prob = model.predict(sequence_scaled, verbose=0)[0][0]
        predicted_label = 1 if pred_prob > 0.5 else 0

        # Get actual value
        actual_price = all_data.iloc[date_idx]['Adj Close']
        prev_price = all_data.iloc[date_idx-1]['Adj Close']
        actual_label = 1 if actual_price > prev_price else 0

        predictions.append({
            "date": pred_date.strftime("%Y-%m-%d"),
            "pred_prob_up": round(float(pred_prob), 4),
            "predicted_label": int(predicted_label),
            "actual_label": int(actual_label)
        })

    return predictions


def determine_result_color(predictions):
    """Determine result color based on prediction accuracy"""
    correct_count = sum(1 for p in predictions if p["predicted_label"] == p["actual_label"])

    if correct_count == 2:
        return "green"
    elif correct_count == 1:
        return "yellow"
    else:
        return "red"


def main():
    """Main function"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    use_volume = len(sys.argv) == 3 and sys.argv[2] == '--volume'

    try:
        # Load and prepare data
        all_data, train_data = load_and_prepare_data(ticker, use_volume)

        # Prepare training data
        if use_volume:
            # Use both price and volume data
            train_features = train_data[['Adj Close', 'Volume']].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_features)

            # Create sequences with volume
            X_train, y_train = create_sequences(train_scaled, use_volume=True)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 2)

            # Build model for 2 features (price + volume)
            model = build_lstm_model((60, 2))
        else:
            # Use price data only (backward compatibility)
            train_prices = train_data['Adj Close'].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_prices.reshape(-1, 1)).flatten()

            # Create sequences without volume
            X_train, y_train = create_sequences(train_scaled, use_volume=False)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 1)

            # Build model for 1 feature (price only)
            model = build_lstm_model((60, 1))

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=2,
            restore_best_weights=True
        )

        # Train with validation split and early stopping
        model.fit(
            X_train, y_train,
            epochs=8,
            batch_size=256,
            validation_split=0.10,
            callbacks=[early_stopping],
            verbose=0
        )

        # Make predictions for target dates
        predictions = predict_for_dates(model, scaler, all_data, PRED_DATES, use_volume)

        # Determine result color
        result_color = determine_result_color(predictions)

        # Add result color to each prediction
        for pred in predictions:
            pred["result_color"] = result_color

        # Create result
        result = {
            "symbol": ticker,
            "train_until": TRAIN_END.strftime("%Y-%m-%d"),
            "predictions": predictions
        }

        # Ensure result directory exists
        RESULT_DIR.mkdir(parents=True, exist_ok=True)

        # Write result file atomically
        result_file = RESULT_DIR / f"{ticker}_20250605.json"
        try:
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2)
        except OSError as e:
            print(f"Error writing result file: {e}", file=sys.stderr)
            sys.exit(1)

        # Print JSON result to stdout
        print(json.dumps(result))

        # Log completion
        logging.info(f"[{ticker}] LSTM completed – result: {result_color}")

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()