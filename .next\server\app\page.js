/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3Nvbmc3JTVDJTVDRGVza3RvcCU1QyU1Q2hvbWUlNUMlNUN1YnVudHUlNUMlNUNmaW5hbmNpYWxfZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29uZzdcXFxcRGVza3RvcFxcXFxob21lXFxcXHVidW50dVxcXFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3Nvbmc3JTVDJTVDRGVza3RvcCU1QyU1Q2hvbWUlNUMlNUN1YnVudHUlNUMlNUNmaW5hbmNpYWxfZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29uZzdcXFxcRGVza3RvcFxcXFxob21lXFxcXHVidW50dVxcXFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_FinancialChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/FinancialChart */ \"(ssr)/./src/components/FinancialChart.tsx\");\n/* harmony import */ var _components_AIChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AIChat */ \"(ssr)/./src/components/AIChat.tsx\");\n/* harmony import */ var _components_SpeedTraffic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SpeedTraffic */ \"(ssr)/./src/components/SpeedTraffic.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    const [currentSymbol, setCurrentSymbol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [showingCompanyList, setShowingCompanyList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChartExpanded, setIsChartExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // 차트 확장 상태\n    // 홈으로 돌아가기 (새로고침 효과)\n    const handleHomeClick = ()=>{\n        console.log('🏠 Home button clicked - resetting all states');\n        setCurrentSymbol(undefined);\n        setShowingCompanyList(false);\n        setIsChartExpanded(false); // 차트 확장 상태 초기화\n        console.log('🔄 Triggering page reload for complete reset');\n        // 페이지 새로고침으로 모든 상태 초기화 (채팅 포함)\n        window.location.reload();\n    };\n    // 차트 확장/축소 토글 (채팅 상태 보존)\n    const handleToggleChartExpand = ()=>{\n        console.log('🔄 Toggling chart expand state:', {\n            current: isChartExpanded,\n            willBe: !isChartExpanded\n        });\n        setIsChartExpanded(!isChartExpanded);\n        console.log('✅ Chart expand toggle completed');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen overflow-hidden bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"relative bg-white border-b border-slate-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 gradient-bg opacity-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative px-4 sm:px-6 py-3 sm:py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"flex items-center space-x-2 sm:space-x-3 hover:bg-slate-50 rounded-lg p-2 -m-2 transition-colors duration-200 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-7 h-7 sm:w-8 sm:h-8 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-200 shadow-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/hanyang-logo.png\",\n                                                alt: \"한양대학교 로고\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg sm:text-xl font-semibold text-slate-900 group-hover:text-blue-600 transition-colors duration-200\",\n                                                    children: \"금융인공지능실무 AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs sm:text-sm text-slate-500 hidden sm:block group-hover:text-slate-600 transition-colors duration-200\",\n                                                    children: \"사용자 맞춤형 투자지원 AI - 2021064802/송승은\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-slate-600\",\n                                            children: \"실시간\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-1 overflow-hidden p-2 sm:p-4 gap-2 sm:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex flex-col flex-1 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${isChartExpanded ? 'fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4' // 확장 시 전체 화면 + 배경\n                                 : showingCompanyList ? 'h-16 flex-shrink-0' // 차트 바 고정 크기\n                                 : currentSymbol ? 'h-64 sm:h-80 flex-shrink-0' // 차트 표시 시 고정 높이로 더 작게\n                                 : 'h-60 sm:h-80 flex-shrink-0' // 기본 차트 크기\n                                } ${isChartExpanded ? '' : 'mb-2 sm:mb-3'} transition-all duration-600 ease-in-out animate-fade-in overflow-hidden`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${isChartExpanded ? 'w-full h-full max-w-7xl max-h-full' : 'h-full'} bg-white ${isChartExpanded ? 'rounded-lg' : 'rounded-lg sm:rounded-xl'} shadow-sm border border-slate-200 overflow-hidden`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FinancialChart__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        symbol: currentSymbol,\n                                        isMinimized: showingCompanyList,\n                                        isExpanded: isChartExpanded,\n                                        onToggleExpand: handleToggleChartExpand\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${isChartExpanded ? 'hidden' // 확장 시 숨김 (display: none으로 DOM은 유지하되 렌더링 안함)\n                                 : showingCompanyList ? 'flex-1' // 기업 리스트 표시 시 전체 공간 사용\n                                 : currentSymbol ? 'flex-1' // 차트 있을 때 나머지 공간 모두 사용\n                                 : 'h-96 sm:h-[32rem]' // 기본 채팅창 크기 증가\n                                } min-h-0 transition-all duration-600 ease-out animate-slide-up`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-white rounded-lg sm:rounded-xl shadow-sm border border-slate-200 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onSymbolSubmit: (s)=>{\n                                            console.log('📊 Symbol submitted from chat:', s);\n                                            setCurrentSymbol(s);\n                                            setShowingCompanyList(false);\n                                        },\n                                        onSymbolError: ()=>{\n                                            console.log('❌ Symbol error from chat');\n                                            setCurrentSymbol(undefined);\n                                            setShowingCompanyList(false);\n                                        },\n                                        onShowingCompanyList: (showing)=>{\n                                            console.log('📋 Company list visibility changed:', showing);\n                                            setShowingCompanyList(showing);\n                                        },\n                                        hasChart: !!currentSymbol,\n                                        showingCompanyList: showingCompanyList,\n                                        isChartExpanded: isChartExpanded\n                                    }, \"persistent-chat\" // 고정 key로 컴포넌트 상태 보존\n                                    , false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"hidden lg:block w-80 animate-fade-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full bg-white rounded-xl shadow-sm border border-slate-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-slate-900 mb-1\",\n                                            children: currentSymbol ? '투자 분석' : '시장 현황'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-500\",\n                                            children: currentSymbol ? 'AI 기반 투자 적격성 분석' : '실시간 시장 지표'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpeedTraffic__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    symbol: currentSymbol\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden fixed bottom-4 right-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105\",\n                            onClick: ()=>{},\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AIChat.tsx":
/*!***********************************!*\
  !*** ./src/components/AIChat.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AIChat({ onSymbolSubmit, onSymbolError, onShowingCompanyList, hasChart, showingCompanyList, isChartExpanded }) {\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMoreButton, setShowMoreButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoadingMore, setIsLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [suggestedQuestions, setSuggestedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHidingSuggestions, setIsHidingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollDiv = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastMessageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 성능 최적화된 질문 예시 생성 (메모이제이션)\n    const QUESTION_POOLS = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AIChat.useMemo[QUESTION_POOLS]\": ()=>({\n                casual: [\n                    '넌 누구니?',\n                    '잘할 수 있어?',\n                    '뭐하고 있어?',\n                    '안녕하세요',\n                    '고마워',\n                    '넌 뭐야?',\n                    '넌 몇 살이야?'\n                ],\n                industry: [\n                    '반도체 산업',\n                    '자동차 관련 기업',\n                    '바이오테크놀로지',\n                    '은행 금융 기업',\n                    '미디어 엔터테인먼트',\n                    '소프트웨어 회사들',\n                    '클라우드 IT 서비스',\n                    '의료기기 회사',\n                    '제약회사들',\n                    '항공우주 방위산업',\n                    '투자 추천해줘',\n                    '어떤 기업이 좋을까?'\n                ],\n                company: [\n                    '테슬라',\n                    '애플',\n                    '마이크로소프트',\n                    '인텔',\n                    '엔비디아',\n                    '구글',\n                    '아마존',\n                    '메타',\n                    'AMD',\n                    '퀄컴'\n                ]\n            })\n    }[\"AIChat.useMemo[QUESTION_POOLS]\"], []);\n    const generateSuggestedQuestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[generateSuggestedQuestions]\": ()=>{\n            const getRandomItem = {\n                \"AIChat.useCallback[generateSuggestedQuestions].getRandomItem\": (arr)=>arr[Math.floor(Math.random() * arr.length)]\n            }[\"AIChat.useCallback[generateSuggestedQuestions].getRandomItem\"];\n            const casualQ = getRandomItem(QUESTION_POOLS.casual);\n            const industryQ1 = getRandomItem(QUESTION_POOLS.industry);\n            let industryQ2 = getRandomItem(QUESTION_POOLS.industry);\n            while(industryQ2 === industryQ1){\n                industryQ2 = getRandomItem(QUESTION_POOLS.industry);\n            }\n            const companyQ = getRandomItem(QUESTION_POOLS.company);\n            return [\n                casualQ,\n                industryQ1,\n                industryQ2,\n                companyQ\n            ];\n        }\n    }[\"AIChat.useCallback[generateSuggestedQuestions]\"], [\n        QUESTION_POOLS\n    ]);\n    // 컴포넌트 마운트 시 질문 예시 생성\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const questions = generateSuggestedQuestions();\n            console.log('Generated suggested questions:', questions);\n            setSuggestedQuestions(questions);\n        }\n    }[\"AIChat.useEffect\"], [\n        generateSuggestedQuestions\n    ]);\n    /* 자동 스크롤 - history 변경 시와 차트 상태 변경 시 */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const scrollToBottom = {\n                \"AIChat.useEffect.scrollToBottom\": ()=>{\n                    // 마지막 메시지로 스크롤 (더 정확함)\n                    if (lastMessageRef.current) {\n                        lastMessageRef.current.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'end',\n                            inline: 'nearest'\n                        });\n                    } else if (scrollDiv.current) {\n                        // fallback: 컨테이너 맨 아래로 스크롤\n                        scrollDiv.current.scrollTo({\n                            top: scrollDiv.current.scrollHeight,\n                            behavior: 'smooth'\n                        });\n                    }\n                }\n            }[\"AIChat.useEffect.scrollToBottom\"];\n            // 약간의 지연을 두고 스크롤 (DOM 업데이트 완료 후)\n            const timer = setTimeout(scrollToBottom, 100);\n            return ({\n                \"AIChat.useEffect\": ()=>clearTimeout(timer)\n            })[\"AIChat.useEffect\"];\n        }\n    }[\"AIChat.useEffect\"], [\n        history,\n        hasChart\n    ]);\n    /* 컨테이너 크기 변경 감지 및 스크롤 재조정 */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const scrollContainer = scrollDiv.current;\n            if (!scrollContainer) return;\n            const resizeObserver = new ResizeObserver({\n                \"AIChat.useEffect\": ()=>{\n                    // 크기 변경 후 스크롤을 맨 아래로\n                    setTimeout({\n                        \"AIChat.useEffect\": ()=>{\n                            if (scrollContainer) {\n                                scrollContainer.scrollTo({\n                                    top: scrollContainer.scrollHeight,\n                                    behavior: 'smooth'\n                                });\n                            }\n                        }\n                    }[\"AIChat.useEffect\"], 50);\n                }\n            }[\"AIChat.useEffect\"]);\n            resizeObserver.observe(scrollContainer);\n            return ({\n                \"AIChat.useEffect\": ()=>{\n                    resizeObserver.disconnect();\n                }\n            })[\"AIChat.useEffect\"];\n        }\n    }[\"AIChat.useEffect\"], []);\n    // 컴포넌트 마운트 시 환영 메시지 가져오기 (차트 상태 변경 시 초기화 방지)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            // 이미 채팅 히스토리가 있으면 환영 메시지를 다시 로드하지 않음 (상태 보존)\n            if (history.length > 0) {\n                console.log('💬 Chat history exists, skipping welcome message reload');\n                return;\n            }\n            const getWelcomeMessage = {\n                \"AIChat.useEffect.getWelcomeMessage\": async ()=>{\n                    console.log('👋 Loading welcome message');\n                    try {\n                        const res = await send({\n                            message: ''\n                        }); // 빈 메시지로 환영 메시지 요청\n                        setHistory([\n                            {\n                                from: 'bot',\n                                text: res.reply\n                            }\n                        ]);\n                        console.log('✅ Welcome message loaded successfully');\n                    } catch (error) {\n                        console.error('❌ Failed to get welcome message:', error);\n                        // 실패 시 기본 환영 메시지\n                        setHistory([\n                            {\n                                from: 'bot',\n                                text: '안녕하세요! 원하시는 소재나 산업 등을 자유롭게 말씀하시면 알맞는 산업을 찾아드리겠습니다.'\n                            }\n                        ]);\n                        console.log('🔄 Fallback welcome message set');\n                    }\n                }\n            }[\"AIChat.useEffect.getWelcomeMessage\"];\n            getWelcomeMessage();\n        }\n    }[\"AIChat.useEffect\"], []); // 마운트 시에만 실행 (history 의존성 제거로 상태 보존)\n    // 성능 최적화된 API 호출 (메모이제이션)\n    const send = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[send]\": async (body)=>{\n            try {\n                const res = await fetch('/api/ai_chat', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(body)\n                });\n                if (!res.ok) {\n                    throw new Error(`HTTP error! status: ${res.status}`);\n                }\n                return await res.json();\n            } catch (error) {\n                console.error('API call failed:', error);\n                throw error;\n            }\n        }\n    }[\"AIChat.useCallback[send]\"], []);\n    // 성능 최적화된 패턴 감지 (메모이제이션)\n    const DETECTION_PATTERNS = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AIChat.useMemo[DETECTION_PATTERNS]\": ()=>({\n                companyList: [\n                    /산업의?\\s*(주요\\s*)?기업들?입니다/,\n                    /분야의?\\s*(대표\\s*)?기업들?입니다/,\n                    /산업에는?\\s*다음과?\\s*같은\\s*기업들?이\\s*있습니다/,\n                    /\\d+\\.\\s*[가-힣A-Za-z\\s]+\\s*\\([A-Z]+\\)/,\n                    /등이\\s*있습니다/,\n                    /관심\\s*있는\\s*기업이\\s*있나요/,\n                    /어떤\\s*회사가\\s*궁금하신가요/\n                ],\n                moreButton: [\n                    /더 많은 기업을 보시려면.*더보기.*말씀해 주세요/,\n                    /총 \\d+개 기업/\n                ]\n            })\n    }[\"AIChat.useMemo[DETECTION_PATTERNS]\"], []);\n    const detectCompanyList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[detectCompanyList]\": (text)=>DETECTION_PATTERNS.companyList.some({\n                \"AIChat.useCallback[detectCompanyList]\": (pattern)=>pattern.test(text)\n            }[\"AIChat.useCallback[detectCompanyList]\"])\n    }[\"AIChat.useCallback[detectCompanyList]\"], [\n        DETECTION_PATTERNS\n    ]);\n    const detectMoreButton = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[detectMoreButton]\": (text)=>DETECTION_PATTERNS.moreButton.some({\n                \"AIChat.useCallback[detectMoreButton]\": (pattern)=>pattern.test(text)\n            }[\"AIChat.useCallback[detectMoreButton]\"])\n    }[\"AIChat.useCallback[detectMoreButton]\"], [\n        DETECTION_PATTERNS\n    ]);\n    // 공통 응답 처리 로직 (메모이제이션)\n    const handleApiResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[handleApiResponse]\": (res)=>{\n            const isShowingCompanies = res.status === 'showing_companies' || detectCompanyList(res.reply);\n            const shouldShowMoreButton = res.hasMore || detectMoreButton(res.reply);\n            if (isShowingCompanies) {\n                onShowingCompanyList?.(true);\n            }\n            setShowMoreButton(shouldShowMoreButton);\n            // 차트 요청 처리\n            if (res.status === 'chart_requested' && res.symbol) {\n                onSymbolSubmit?.(res.symbol);\n                onShowingCompanyList?.(false);\n                setShowMoreButton(false);\n                // 스크롤 재조정\n                setTimeout({\n                    \"AIChat.useCallback[handleApiResponse]\": ()=>{\n                        scrollDiv.current?.scrollTo({\n                            top: scrollDiv.current.scrollHeight,\n                            behavior: 'smooth'\n                        });\n                    }\n                }[\"AIChat.useCallback[handleApiResponse]\"], 200);\n                // 세션 리셋\n                setTimeout({\n                    \"AIChat.useCallback[handleApiResponse]\": async ()=>{\n                        try {\n                            await send({\n                                message: '__RESET_SESSION__'\n                            });\n                        } catch (error) {\n                            console.error('Failed to reset session:', error);\n                        }\n                    }\n                }[\"AIChat.useCallback[handleApiResponse]\"], 1000);\n            } else if (res.status === 'error') {\n                onSymbolError?.();\n                onShowingCompanyList?.(false);\n                setShowMoreButton(false);\n            }\n        }\n    }[\"AIChat.useCallback[handleApiResponse]\"], [\n        detectCompanyList,\n        detectMoreButton,\n        onShowingCompanyList,\n        onSymbolSubmit,\n        onSymbolError,\n        send\n    ]);\n    // 최적화된 질문 예시 버튼 클릭 핸들러\n    const handleSuggestedQuestionClick = async (question)=>{\n        // 질문 예시 버튼 부드럽게 숨기기\n        setIsHidingSuggestions(true);\n        setTimeout(()=>{\n            setSuggestedQuestions([]);\n            setIsHidingSuggestions(false);\n        }, 300);\n        // 사용자 메시지로 추가\n        setHistory((h)=>[\n                ...h,\n                {\n                    from: 'user',\n                    text: question\n                }\n            ]);\n        try {\n            const res = await send({\n                message: question,\n                history\n            });\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: res.reply\n                    }\n                ]);\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('Suggested question error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 일시적인 오류가 발생했습니다.'\n                    }\n                ]);\n            onSymbolError?.();\n            onShowingCompanyList?.(false);\n            setShowMoreButton(false);\n        }\n    };\n    // 최적화된 더보기 버튼 클릭 핸들러\n    const handleMoreClick = async ()=>{\n        setIsLoadingMore(true);\n        setShowMoreButton(false);\n        try {\n            const res = await send({\n                message: '더보기',\n                history\n            });\n            // 마지막 봇 메시지를 새로운 전체 리스트로 대체\n            setHistory((h)=>{\n                const newHistory = [\n                    ...h\n                ];\n                for(let i = newHistory.length - 1; i >= 0; i--){\n                    if (newHistory[i].from === 'bot') {\n                        newHistory[i] = {\n                            from: 'bot',\n                            text: res.reply\n                        };\n                        break;\n                    }\n                }\n                return newHistory;\n            });\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('More companies error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 더보기 요청 중 오류가 발생했습니다.'\n                    }\n                ]);\n        } finally{\n            setIsLoadingMore(false);\n        }\n    };\n    // 최적화된 메인 제출 핸들러\n    const onSubmit = async (e)=>{\n        e.preventDefault();\n        const text = inputRef.current?.value.trim();\n        if (!text) return;\n        setHistory((h)=>[\n                ...h,\n                {\n                    from: 'user',\n                    text\n                }\n            ]);\n        inputRef.current.value = '';\n        // 질문 예시 버튼 숨기기\n        if (suggestedQuestions.length > 0) {\n            setIsHidingSuggestions(true);\n            setTimeout(()=>{\n                setSuggestedQuestions([]);\n                setIsHidingSuggestions(false);\n            }, 300);\n        }\n        try {\n            const res = await send({\n                message: text,\n                history\n            });\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: res.reply\n                    }\n                ]);\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('Chat error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 일시적인 오류가 발생했습니다.'\n                    }\n                ]);\n            onSymbolError?.();\n            onShowingCompanyList?.(false);\n            setShowMoreButton(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full max-h-full relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 sm:px-4 py-2 border-b border-slate-100 bg-slate-50/50 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold text-slate-900\",\n                                children: \"AI 어시스턴트\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollDiv,\n                className: \"flex-1 min-h-0 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4 scroll-smooth bg-gradient-to-b from-slate-50/30 to-white\",\n                style: {\n                    scrollBehavior: 'smooth',\n                    transition: 'all 700ms cubic-bezier(0.4, 0, 0.2, 1)',\n                    maxHeight: showingCompanyList ? 'calc(100vh - 200px)' // 질문 블럭 공간 고려\n                     : hasChart ? '180px' : '280px',\n                    height: showingCompanyList ? 'calc(100vh - 200px)' : hasChart ? '180px' : '280px'\n                },\n                children: [\n                    history.map((m, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: i === history.length - 1 ? lastMessageRef : null,\n                            className: `flex ${m.from === 'user' ? 'justify-end' : 'justify-start'} animate-slide-up`,\n                            children: m.from === 'user' ? // 사용자 메시지 (오른쪽 정렬)\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start flex-row-reverse max-w-[85%] sm:max-w-[80%]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm bg-gradient-to-br from-blue-500 to-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 rounded-xl shadow-sm whitespace-pre-line bg-gradient-to-br from-blue-500 to-blue-600 text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm leading-relaxed\",\n                                            children: m.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this) : // AI 메시지 (왼쪽 정렬)\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start max-w-[85%] sm:max-w-[80%]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm bg-gradient-to-br from-slate-100 to-slate-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-slate-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 rounded-xl shadow-sm whitespace-pre-line bg-white border border-slate-200 text-slate-900\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm leading-relaxed\",\n                                            children: m.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)),\n                    showMoreButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleMoreClick,\n                            disabled: isLoadingMore,\n                            className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2\",\n                            children: isLoadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"로딩중...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 9l-7 7-7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"더보기\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            suggestedQuestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-3 py-3 bg-slate-50/50 border-t border-slate-100 flex-shrink-0 transition-all duration-700 ease-out ${isHidingSuggestions ? 'opacity-0 transform translate-y-2' : 'opacity-100 transform translate-y-0 animate-slide-up'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-slate-500 mb-3 text-center\",\n                        children: \"\\uD83D\\uDCA1 이런 질문은 어떠세요?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 justify-center\",\n                        children: suggestedQuestions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSuggestedQuestionClick(question),\n                                className: `px-3 py-1.5 bg-white hover:bg-slate-50 text-slate-700 text-xs rounded-lg transition-all duration-200 border border-slate-200 shadow-sm ${isHidingSuggestions ? 'opacity-0' : 'opacity-100'}`,\n                                style: {\n                                    animationDelay: isHidingSuggestions ? '0ms' : `${index * 100}ms`\n                                },\n                                children: question\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 sm:p-3 border-t border-slate-100 bg-white flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: onSubmit,\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                placeholder: \"메시지를 입력하세요...\",\n                                className: \"w-full input-modern pr-9 text-sm py-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"absolute right-1 top-1/2 transform -translate-y-1/2 w-7 h-7 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center justify-center transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3 h-3\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIChat.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FinancialChart.tsx":
/*!*******************************************!*\
  !*** ./src/components/FinancialChart.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lightweight_charts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lightweight-charts */ \"(ssr)/./node_modules/lightweight-charts/dist/lightweight-charts.development.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FinancialChart = ({ symbol, isMinimized, isExpanded, onToggleExpand })=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const seriesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ESC 키로 확장 모드 종료\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FinancialChart.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && isExpanded) {\n                        onToggleExpand?.();\n                    }\n                }\n            }[\"FinancialChart.useEffect.handleKeyDown\"];\n            if (isExpanded) {\n                document.addEventListener('keydown', handleKeyDown);\n                return ({\n                    \"FinancialChart.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n                })[\"FinancialChart.useEffect\"];\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        isExpanded,\n        onToggleExpand\n    ]);\n    // 확장 상태 변경 시 차트 크기만 조정 (재생성 방지로 채팅 상태 보존)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('🔄 Chart expand state changed:', {\n                isExpanded,\n                hasChart: !!chartRef.current\n            });\n            if (!chartRef.current) {\n                console.log('⚠️ No chart instance found, skipping resize');\n                return;\n            }\n            try {\n                // 확장 상태에 따른 새로운 높이 계산\n                const newHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                console.log('📏 Resizing chart to height:', newHeight);\n                // 차트 크기만 변경 (재생성하지 않음으로써 채팅 상태 보존)\n                chartRef.current.applyOptions({\n                    height: newHeight\n                });\n                // 차트 내용을 새로운 크기에 맞게 조정\n                chartRef.current.timeScale().fitContent();\n                console.log('✅ Chart resize completed successfully');\n            } catch (error) {\n                console.error('❌ Chart resize failed:', error);\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        isExpanded\n    ]);\n    // 차트 초기화 - symbol이 있을 때만 생성\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('🎯 Chart initialization effect triggered:', {\n                symbol,\n                hasChart: !!chartRef.current\n            });\n            if (!ref.current || !symbol) {\n                console.log('🧹 Cleaning up chart (no symbol or ref)');\n                // symbol이 없으면 기존 차트 제거\n                if (chartRef.current) {\n                    chartRef.current.remove();\n                    chartRef.current = null;\n                    seriesRef.current = null;\n                    console.log('✅ Chart cleanup completed');\n                }\n                return;\n            }\n            // 이미 차트가 있다면 스킵 (중복 생성 방지)\n            if (chartRef.current) {\n                console.log('⏭️ Chart already exists, skipping initialization');\n                return;\n            }\n            console.log('🏗️ Creating new chart instance');\n            try {\n                // 초기 차트 높이 설정\n                const initialHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                console.log('📐 Initial chart height:', initialHeight);\n                // 새로운 차트 생성\n                chartRef.current = (0,lightweight_charts__WEBPACK_IMPORTED_MODULE_2__.createChart)(ref.current, {\n                    height: initialHeight,\n                    layout: {\n                        background: {\n                            color: '#ffffff'\n                        },\n                        textColor: '#333'\n                    },\n                    grid: {\n                        vertLines: {\n                            color: '#f0f0f0'\n                        },\n                        horzLines: {\n                            color: '#f0f0f0'\n                        }\n                    },\n                    timeScale: {\n                        borderColor: '#cccccc',\n                        timeVisible: true,\n                        secondsVisible: false\n                    },\n                    rightPriceScale: {\n                        borderColor: '#cccccc',\n                        scaleMargins: {\n                            top: 0.1,\n                            bottom: 0.1\n                        }\n                    },\n                    watermark: {\n                        visible: false\n                    },\n                    crosshair: {\n                        mode: 1\n                    },\n                    handleScroll: {\n                        mouseWheel: true,\n                        pressedMouseMove: true\n                    },\n                    handleScale: {\n                        axisPressedMouseMove: true,\n                        mouseWheel: true,\n                        pinch: true\n                    }\n                });\n                // 라인 시리즈 추가\n                seriesRef.current = chartRef.current.addLineSeries({\n                    color: '#2563eb',\n                    lineWidth: 2,\n                    priceFormat: {\n                        type: 'price',\n                        precision: 2,\n                        minMove: 0.01\n                    },\n                    crosshairMarkerVisible: true,\n                    crosshairMarkerRadius: 4,\n                    lastValueVisible: true,\n                    priceLineVisible: true\n                });\n                console.log('✅ Chart instance created successfully');\n                // 윈도우 리사이즈 이벤트 리스너 추가\n                const handleResize = {\n                    \"FinancialChart.useEffect.handleResize\": ()=>{\n                        console.log('🔄 Window resize detected');\n                        if (chartRef.current) {\n                            const newHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                            console.log('📏 Applying new height on resize:', newHeight);\n                            chartRef.current.applyOptions({\n                                height: newHeight\n                            });\n                            chartRef.current.timeScale().fitContent();\n                        }\n                    }\n                }[\"FinancialChart.useEffect.handleResize\"];\n                window.addEventListener('resize', handleResize);\n                return ({\n                    \"FinancialChart.useEffect\": ()=>{\n                        console.log('🧹 Cleaning up resize listener');\n                        window.removeEventListener('resize', handleResize);\n                    }\n                })[\"FinancialChart.useEffect\"];\n            } catch (error) {\n                console.error('❌ Chart initialization failed:', error);\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        symbol\n    ]);\n    // 심볼이 변경될 때 데이터 로드\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('📊 Data loading effect triggered:', {\n                symbol,\n                hasChart: !!chartRef.current,\n                hasSeries: !!seriesRef.current\n            });\n            if (!symbol || !seriesRef.current || !chartRef.current) {\n                console.log('⏭️ Skipping data load - missing requirements');\n                return;\n            }\n            const loadChartData = {\n                \"FinancialChart.useEffect.loadChartData\": async ()=>{\n                    console.log('🔄 Starting chart data load for symbol:', symbol);\n                    setIsLoading(true);\n                    setError(null);\n                    try {\n                        const res = await fetch(`/api/csv_chart_data?symbol=${symbol}`);\n                        const data = await res.json();\n                        if (data.error) {\n                            console.error('❌ Chart data API error:', data.error);\n                            setError(data.error);\n                            return;\n                        }\n                        console.log('📈 Processing chart data, points count:', data.data?.length || 0);\n                        // 데이터를 lightweight-charts 형식으로 변환\n                        const chartData = data.data.map({\n                            \"FinancialChart.useEffect.loadChartData.chartData\": (point)=>({\n                                    time: point.time,\n                                    value: point.value\n                                })\n                        }[\"FinancialChart.useEffect.loadChartData.chartData\"]);\n                        // 시리즈에 데이터 설정\n                        seriesRef.current?.setData(chartData);\n                        // 차트를 데이터에 맞게 조정\n                        if (chartRef.current) {\n                            chartRef.current.timeScale().fitContent();\n                            console.log('✅ Chart data loaded and fitted successfully');\n                        }\n                    } catch (error) {\n                        console.error('❌ Failed to load chart data:', error);\n                        setError('차트 데이터를 불러오는 중 오류가 발생했습니다.');\n                    } finally{\n                        setIsLoading(false);\n                        console.log('🏁 Chart data loading completed');\n                    }\n                }\n            }[\"FinancialChart.useEffect.loadChartData\"];\n            // 약간의 지연을 두고 데이터 로드 (차트 초기화 완료 후)\n            console.log('⏰ Scheduling data load with 200ms delay');\n            const timer = setTimeout(loadChartData, 200);\n            return ({\n                \"FinancialChart.useEffect\": ()=>{\n                    console.log('🧹 Cleaning up data load timer');\n                    clearTimeout(timer);\n                }\n            })[\"FinancialChart.useEffect\"];\n        }\n    }[\"FinancialChart.useEffect\"], [\n        symbol\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full flex flex-col\",\n        children: symbol ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-2 border-b border-slate-100 bg-gradient-to-r from-slate-50 to-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-7 h-7 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-slate-900\",\n                                                    children: [\n                                                        symbol,\n                                                        \" 주가 차트 \",\n                                                        isExpanded && '(확장 모드)'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-500\",\n                                                    children: isExpanded ? 'ESC 키로 닫기' : '실시간 차트'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-amber-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-amber-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"로딩중\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"오류\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"실시간\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        symbol && onToggleExpand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onToggleExpand,\n                                            className: \"px-2 py-1 text-xs bg-slate-500 hover:bg-slate-600 text-white rounded-md transition-colors duration-200 flex items-center space-x-1\",\n                                            title: isExpanded ? \"차트 축소\" : \"차트 확장\",\n                                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"축소\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"확장\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-red-500\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-red-700\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, undefined),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-h-0 relative bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: ref,\n                            className: \"absolute inset-0 w-full h-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 15\n                        }, undefined),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-purple-500 rounded-full animate-spin mx-auto\",\n                                                style: {\n                                                    animationDirection: 'reverse',\n                                                    animationDuration: '1.5s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-slate-900 mb-2\",\n                                        children: \"차트 데이터 로딩 중\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500\",\n                                        children: \"최신 시장 정보를 가져오는 중...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true) : isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center h-full bg-gradient-to-r from-slate-50 to-white px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold text-slate-900\",\n                                children: \"차트 생성 대기중\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                lineNumber: 339,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n            lineNumber: 338,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-slate-50 to-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center p-12 max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-blue-500\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1.5,\n                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-slate-900 mb-3\",\n                        children: \"분석 준비 완료\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600 mb-4 leading-relaxed\",\n                        children: \"아래 AI 어시스턴트를 통해 관심 있는 기업이나 산업을 검색해보세요.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 text-sm text-slate-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"실시간 시장 데이터 기반\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                lineNumber: 357,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n            lineNumber: 356,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(FinancialChart));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FinancialChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SpeedTraffic.tsx":
/*!*****************************************!*\
  !*** ./src/components/SpeedTraffic.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SpeedTraffic = ({ symbol })=>{\n    // Market indicators state (Pre-ticker mode)\n    const [indicators, setIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Traffic lights state (Post-ticker mode) - New order\n    const [technicalLight, setTechnicalLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 1: Technical Analysis\n    const [industryLight, setIndustryLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 2: Industry Sensitivity\n    const [marketLight, setMarketLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 3: Market Sensitivity (CAPM)\n    const [riskLight, setRiskLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 4: Volatility Risk\n    const [neuralLight, setNeuralLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 5: Neural Network (LSTM)\n    // Prediction state\n    const [phase1Loading, setPhase1Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phase2Loading, setPhase2Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lstmLoading, setLstmLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [predictionError, setPredictionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTimeoutMessage, setShowTimeoutMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastRequestTime, setLastRequestTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Single-flight guard\n    const inFlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 실제 시장 데이터 가져오기 (Pre-ticker mode)\n    const fetchMarketData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/market_data');\n            const result = await response.json();\n            if (result.success && result.data) {\n                const formattedData = result.data.map((item)=>({\n                        label: item.label,\n                        value: item.price.toLocaleString('en-US', {\n                            minimumFractionDigits: 2,\n                            maximumFractionDigits: 2\n                        }),\n                        change: `${item.changePercent >= 0 ? '+' : ''}${item.changePercent.toFixed(2)}%`,\n                        trend: item.trend,\n                        color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'\n                    }));\n                setIndicators(formattedData);\n                setLastUpdate(new Date().toLocaleTimeString('ko-KR'));\n            }\n        } catch (error) {\n            console.error('Failed to fetch market data:', error);\n            // 에러 시 fallback 데이터 사용\n            setIndicators([\n                {\n                    label: 'S&P 500',\n                    value: '4,567.89',\n                    change: '+1.2%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '나스닥',\n                    value: '14,234.56',\n                    change: '+0.8%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '다우존스',\n                    value: '34,567.12',\n                    change: '-0.3%',\n                    trend: 'down',\n                    color: 'red'\n                },\n                {\n                    label: 'VIX',\n                    value: '18.45',\n                    change: '-2.1%',\n                    trend: 'down',\n                    color: 'yellow'\n                },\n                {\n                    label: '달러/원',\n                    value: '1,327.50',\n                    change: '+0.5%',\n                    trend: 'up',\n                    color: 'green'\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Convert result color to traffic light status\n    const resultColorToStatus = (color)=>{\n        switch(color?.toLowerCase()){\n            case 'green':\n            case 'good':\n                return 'good';\n            case 'yellow':\n            case 'warning':\n                return 'warning';\n            case 'red':\n            case 'danger':\n                return 'danger';\n            default:\n                return 'warning';\n        }\n    };\n    // Extract color from LSTM/MFI results\n    const getColorFromResult = (obj)=>{\n        if (!obj) return undefined;\n        if (typeof obj === 'string') return obj;\n        if (obj.result_color) return obj.result_color;\n        if (obj.traffic_light) return obj.traffic_light;\n        if (obj.color) return obj.color;\n        return undefined;\n    };\n    // Staged execution: Phase 1 (Fast services) + Phase 2 (LSTM)\n    const fetchStagedPrediction = async ()=>{\n        if (!symbol || inFlight.current) return;\n        // Prevent too frequent requests (minimum 10 seconds between requests)\n        const now = Date.now();\n        if (now - lastRequestTime < 10000) {\n            console.log('Prediction request throttled - too frequent');\n            return;\n        }\n        try {\n            // Set single-flight guard\n            inFlight.current = true;\n            setLastRequestTime(now);\n            // Reset all lights to inactive state\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n            setPredictionError(null);\n            setShowTimeoutMessage(false);\n            console.log(`[SpeedTraffic] Starting staged prediction for ${symbol}`);\n            // Phase 1: Execute fast services (Technical, Industry, Market, Volatility)\n            setPhase1Loading(true);\n            console.log(`[SpeedTraffic] Phase 1: Starting fast services for ${symbol}`);\n            const phase1Response = await fetch(`/api/speedtraffic_staged?symbol=${symbol}&stage=phase1`, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!phase1Response.ok) {\n                throw new Error(`Phase 1 HTTP ${phase1Response.status}: ${phase1Response.statusText}`);\n            }\n            const phase1Result = await phase1Response.json();\n            console.log(`[SpeedTraffic] Phase 1 result:`, phase1Result);\n            // Update lights 1-4 immediately after Phase 1 completes\n            if (phase1Result.traffic_lights) {\n                if (phase1Result.traffic_lights.technical) {\n                    setTechnicalLight(resultColorToStatus(phase1Result.traffic_lights.technical));\n                    console.log(`[SpeedTraffic] Technical light set to: ${phase1Result.traffic_lights.technical}`);\n                }\n                if (phase1Result.traffic_lights.industry) {\n                    setIndustryLight(resultColorToStatus(phase1Result.traffic_lights.industry));\n                    console.log(`[SpeedTraffic] Industry light set to: ${phase1Result.traffic_lights.industry}`);\n                }\n                if (phase1Result.traffic_lights.market) {\n                    setMarketLight(resultColorToStatus(phase1Result.traffic_lights.market));\n                    console.log(`[SpeedTraffic] Market light set to: ${phase1Result.traffic_lights.market}`);\n                }\n                if (phase1Result.traffic_lights.risk) {\n                    setRiskLight(resultColorToStatus(phase1Result.traffic_lights.risk));\n                    console.log(`[SpeedTraffic] Risk light set to: ${phase1Result.traffic_lights.risk}`);\n                }\n            }\n            setPhase1Loading(false);\n            console.log(`[SpeedTraffic] Phase 1 completed successfully for ${symbol}`);\n            // Phase 2: Execute LSTM service\n            setPhase2Loading(true);\n            setLstmLoading(true);\n            console.log(`[SpeedTraffic] Phase 2: Starting LSTM service for ${symbol}`);\n            // Start 20-second timer for Korean timeout message\n            const timeoutTimer = setTimeout(()=>{\n                setShowTimeoutMessage(true);\n            }, 20000);\n            const phase2Response = await fetch(`/api/speedtraffic_staged?symbol=${symbol}&stage=phase2`, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(60000)\n            });\n            clearTimeout(timeoutTimer);\n            if (!phase2Response.ok) {\n                throw new Error(`Phase 2 HTTP ${phase2Response.status}: ${phase2Response.statusText}`);\n            }\n            const phase2Result = await phase2Response.json();\n            console.log(`[SpeedTraffic] Phase 2 result:`, phase2Result);\n            // Update light 5 after Phase 2 completes\n            if (phase2Result.traffic_lights && phase2Result.traffic_lights.neural) {\n                setNeuralLight(resultColorToStatus(phase2Result.traffic_lights.neural));\n                console.log(`[SpeedTraffic] Neural light set to: ${phase2Result.traffic_lights.neural}`);\n            }\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            console.log(`[SpeedTraffic] Phase 2 completed successfully for ${symbol}`);\n            console.log(`[SpeedTraffic] All phases completed successfully for ${symbol}`);\n        } catch (error) {\n            console.error('Staged prediction error:', error);\n            if (error instanceof Error) {\n                if (error.name === 'TimeoutError') {\n                    setPredictionError('요청 시간 초과');\n                } else {\n                    setPredictionError(`예측 실패: ${error.message}`);\n                }\n            } else {\n                setPredictionError('예측 서비스 연결 실패');\n            }\n            // Reset all lights to inactive on error\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n        } finally{\n            setPhase1Loading(false);\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            setShowTimeoutMessage(false);\n            inFlight.current = false;\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (symbol) {\n                // When symbol is provided, fetch staged prediction once\n                fetchStagedPrediction();\n            } else {\n                // When no symbol, fetch market data initially\n                fetchMarketData();\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (!symbol) {\n                const interval = setInterval({\n                    \"SpeedTraffic.useEffect.interval\": ()=>{\n                        fetchMarketData();\n                    }\n                }[\"SpeedTraffic.useEffect.interval\"], 20000);\n                return ({\n                    \"SpeedTraffic.useEffect\": ()=>clearInterval(interval)\n                })[\"SpeedTraffic.useEffect\"];\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // Utility functions for rendering\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case 'up':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M7 17l9.2-9.2M17 17V7H7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, undefined);\n            case 'down':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 7l-9.2 9.2M7 7v10h10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-slate-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 12H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const getChangeColor = (trend)=>{\n        switch(trend){\n            case 'up':\n                return 'text-green-600';\n            case 'down':\n                return 'text-red-600';\n            default:\n                return 'text-slate-500';\n        }\n    };\n    const getTrafficLightColor = (status)=>{\n        switch(status){\n            case 'good':\n                return 'bg-green-500';\n            case 'warning':\n                return 'bg-yellow-500';\n            case 'danger':\n                return 'bg-red-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'good':\n                return '양호';\n            case 'warning':\n                return '보통';\n            case 'danger':\n                return '주의';\n            default:\n                return '분석중';\n        }\n    };\n    // Post-ticker mode: Traffic lights display\n    if (symbol) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-slate-900 mb-1\",\n                            children: \"SpeedTraffic™\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-600\",\n                            children: [\n                                symbol,\n                                \" 투자 적격성\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse mx-auto mt-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-xl border border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight)} shadow-lg transition-all duration-700 ease-in-out ${technicalLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        technicalLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(technicalLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"기술적 분석\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${technicalLight === 'inactive' ? 'text-gray-400' : technicalLight === 'good' ? 'text-green-300' : technicalLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${industryLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(industryLight)} shadow-lg transition-all duration-700 ease-in-out ${industryLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        industryLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(industryLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${industryLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"산업 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${industryLight === 'inactive' ? 'text-gray-400' : industryLight === 'good' ? 'text-green-300' : industryLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: industryLight === 'inactive' ? '대기중' : getStatusText(industryLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${marketLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(marketLight)} shadow-lg transition-all duration-700 ease-in-out ${marketLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        marketLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(marketLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${marketLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"시장 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${marketLight === 'inactive' ? 'text-gray-400' : marketLight === 'good' ? 'text-green-300' : marketLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: marketLight === 'inactive' ? '대기중' : getStatusText(marketLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight)} shadow-lg transition-all duration-700 ease-in-out ${riskLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        riskLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(riskLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"변동성 리스크\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${riskLight === 'inactive' ? 'text-gray-400' : riskLight === 'good' ? 'text-green-300' : riskLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${neuralLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(neuralLight)} shadow-lg transition-all duration-700 ease-in-out ${neuralLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        neuralLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(neuralLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        lstmLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full border-2 border-blue-500 border-t-transparent animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${neuralLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: lstmLoading ? '딥러닝 기반 예측(LSTM)' : '신경망 예측(LSTM)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${neuralLight === 'inactive' ? 'text-gray-400' : neuralLight === 'good' ? 'text-green-300' : neuralLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: lstmLoading ? '분석중...' : neuralLight === 'inactive' ? '대기중' : getStatusText(neuralLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, undefined),\n                phase1Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-2 border-orange-500 border-t-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"기술적 분석, 산업군, 시장 민감도, 변동성 위험 분석 중...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, undefined),\n                phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"딥러닝 기반 예측(LSTM) 분석 중...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, undefined),\n                showTimeoutMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-600 text-center\",\n                        children: \"이 작업은 시간이 걸립니다... (최대 60초)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-red-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"오류: \",\n                                    predictionError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && !phase1Loading && !phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchStagedPrediction,\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors\",\n                        children: \"다시 시도\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n            lineNumber: 353,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pre-ticker mode: Market indicators display\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-slate-900\",\n                        children: \"시장 현황\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-2 h-2 rounded-full animate-pulse ${loading ? 'bg-yellow-400' : 'bg-green-400'}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: loading && indicators.length === 0 ? // 로딩 스켈레톤\n                Array.from({\n                    length: 5\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-slate-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-slate-200 rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 13\n                    }, undefined)) : indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-slate-700\",\n                                        children: indicator.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    getTrendIcon(indicator.trend)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-slate-900\",\n                                        children: indicator.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${getChangeColor(indicator.trend)}`,\n                                        children: indicator.change\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-3 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-2 text-xs text-slate-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: loading ? '업데이트 중...' : lastUpdate ? `마지막 업데이트: ${lastUpdate}` : '20초마다 업데이트'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 634,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n        lineNumber: 589,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpeedTraffic);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TcGVlZFRyYWZmaWMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEyRDtBQW9EM0QsTUFBTUksZUFBNEMsQ0FBQyxFQUFFQyxNQUFNLEVBQUU7SUFDM0QsNENBQTRDO0lBQzVDLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHTiwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUNsRSxNQUFNLENBQUNPLFNBQVNDLFdBQVcsR0FBR1IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDUyxZQUFZQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFTO0lBRXJELHNEQUFzRDtJQUN0RCxNQUFNLENBQUNXLGdCQUFnQkMsa0JBQWtCLEdBQUdaLCtDQUFRQSxDQUE2QyxhQUFhLDhCQUE4QjtJQUM1SSxNQUFNLENBQUNhLGVBQWVDLGlCQUFpQixHQUFHZCwrQ0FBUUEsQ0FBNkMsYUFBYSxnQ0FBZ0M7SUFDNUksTUFBTSxDQUFDZSxhQUFhQyxlQUFlLEdBQUdoQiwrQ0FBUUEsQ0FBNkMsYUFBYSxxQ0FBcUM7SUFDN0ksTUFBTSxDQUFDaUIsV0FBV0MsYUFBYSxHQUFHbEIsK0NBQVFBLENBQTZDLGFBQWEsMkJBQTJCO0lBQy9ILE1BQU0sQ0FBQ21CLGFBQWFDLGVBQWUsR0FBR3BCLCtDQUFRQSxDQUE2QyxhQUFhLGlDQUFpQztJQUV6SSxtQkFBbUI7SUFDbkIsTUFBTSxDQUFDcUIsZUFBZUMsaUJBQWlCLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN1QixlQUFlQyxpQkFBaUIsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3lCLGFBQWFDLGVBQWUsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzJCLGlCQUFpQkMsbUJBQW1CLEdBQUc1QiwrQ0FBUUEsQ0FBZ0I7SUFDdEUsTUFBTSxDQUFDNkIsb0JBQW9CQyxzQkFBc0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQytCLGlCQUFpQkMsbUJBQW1CLEdBQUdoQywrQ0FBUUEsQ0FBUztJQUUvRCxzQkFBc0I7SUFDdEIsTUFBTWlDLFdBQVcvQiw2Q0FBTUEsQ0FBQztJQUV4QixtQ0FBbUM7SUFDbkMsTUFBTWdDLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YxQixXQUFXO1lBQ1gsTUFBTTJCLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxJQUFJRixPQUFPRyxJQUFJLEVBQUU7Z0JBQ2pDLE1BQU1DLGdCQUFnQkosT0FBT0csSUFBSSxDQUFDRSxHQUFHLENBQUMsQ0FBQ0MsT0FBZTt3QkFDcERDLE9BQU9ELEtBQUtDLEtBQUs7d0JBQ2pCQyxPQUFPRixLQUFLRyxLQUFLLENBQUNDLGNBQWMsQ0FBQyxTQUFTOzRCQUN4Q0MsdUJBQXVCOzRCQUN2QkMsdUJBQXVCO3dCQUN6Qjt3QkFDQUMsUUFBUSxHQUFHUCxLQUFLUSxhQUFhLElBQUksSUFBSSxNQUFNLEtBQUtSLEtBQUtRLGFBQWEsQ0FBQ0MsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDO3dCQUNoRkMsT0FBT1YsS0FBS1UsS0FBSzt3QkFDakJDLE9BQU9YLEtBQUtVLEtBQUssS0FBSyxPQUFPLFVBQVVWLEtBQUtVLEtBQUssS0FBSyxTQUFTLFFBQVE7b0JBQ3pFO2dCQUVBL0MsY0FBY21DO2dCQUNkL0IsY0FBYyxJQUFJNkMsT0FBT0Msa0JBQWtCLENBQUM7WUFDOUM7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsdUJBQXVCO1lBQ3ZCbkQsY0FBYztnQkFDWjtvQkFBRXNDLE9BQU87b0JBQVdDLE9BQU87b0JBQVlLLFFBQVE7b0JBQVNHLE9BQU87b0JBQU1DLE9BQU87Z0JBQVE7Z0JBQ3BGO29CQUFFVixPQUFPO29CQUFPQyxPQUFPO29CQUFhSyxRQUFRO29CQUFTRyxPQUFPO29CQUFNQyxPQUFPO2dCQUFRO2dCQUNqRjtvQkFBRVYsT0FBTztvQkFBUUMsT0FBTztvQkFBYUssUUFBUTtvQkFBU0csT0FBTztvQkFBUUMsT0FBTztnQkFBTTtnQkFDbEY7b0JBQUVWLE9BQU87b0JBQU9DLE9BQU87b0JBQVNLLFFBQVE7b0JBQVNHLE9BQU87b0JBQVFDLE9BQU87Z0JBQVM7Z0JBQ2hGO29CQUFFVixPQUFPO29CQUFRQyxPQUFPO29CQUFZSyxRQUFRO29CQUFTRyxPQUFPO29CQUFNQyxPQUFPO2dCQUFRO2FBQ2xGO1FBQ0gsU0FBVTtZQUNSOUMsV0FBVztRQUNiO0lBQ0Y7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTW1ELHNCQUFzQixDQUFDTDtRQUMzQixPQUFRQSxPQUFPTTtZQUNiLEtBQUs7WUFDTCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO1lBQ0wsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixJQUFJLENBQUNBLEtBQUssT0FBT0M7UUFDakIsSUFBSSxPQUFPRCxRQUFRLFVBQVUsT0FBT0E7UUFDcEMsSUFBSUEsSUFBSUUsWUFBWSxFQUFFLE9BQU9GLElBQUlFLFlBQVk7UUFDN0MsSUFBSUYsSUFBSUcsYUFBYSxFQUFFLE9BQU9ILElBQUlHLGFBQWE7UUFDL0MsSUFBSUgsSUFBSVIsS0FBSyxFQUFFLE9BQU9RLElBQUlSLEtBQUs7UUFDL0IsT0FBT1M7SUFDVDtJQUVBLDZEQUE2RDtJQUM3RCxNQUFNRyx3QkFBd0I7UUFDNUIsSUFBSSxDQUFDOUQsVUFBVTZCLFNBQVNrQyxPQUFPLEVBQUU7UUFFakMsc0VBQXNFO1FBQ3RFLE1BQU1DLE1BQU1iLEtBQUthLEdBQUc7UUFDcEIsSUFBSUEsTUFBTXJDLGtCQUFrQixPQUFPO1lBQ2pDMkIsUUFBUVcsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRiwwQkFBMEI7WUFDMUJwQyxTQUFTa0MsT0FBTyxHQUFHO1lBQ25CbkMsbUJBQW1Cb0M7WUFFbkIscUNBQXFDO1lBQ3JDeEQsa0JBQWtCO1lBQ2xCRSxpQkFBaUI7WUFDakJFLGVBQWU7WUFDZkUsYUFBYTtZQUNiRSxlQUFlO1lBRWZRLG1CQUFtQjtZQUNuQkUsc0JBQXNCO1lBRXRCNEIsUUFBUVcsR0FBRyxDQUFDLENBQUMsOENBQThDLEVBQUVqRSxRQUFRO1lBRXJFLDJFQUEyRTtZQUMzRWtCLGlCQUFpQjtZQUNqQm9DLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLG1EQUFtRCxFQUFFakUsUUFBUTtZQUUxRSxNQUFNa0UsaUJBQWlCLE1BQU1sQyxNQUFNLENBQUMsZ0NBQWdDLEVBQUVoQyxPQUFPLGFBQWEsQ0FBQyxFQUFFO2dCQUMzRm1FLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsVUFBVTtnQkFDWjtnQkFDQUMsUUFBUUMsWUFBWUMsT0FBTyxDQUFDO1lBQzlCO1lBRUEsSUFBSSxDQUFDTCxlQUFlTSxFQUFFLEVBQUU7Z0JBQ3RCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLGFBQWEsRUFBRVAsZUFBZVEsTUFBTSxDQUFDLEVBQUUsRUFBRVIsZUFBZVMsVUFBVSxFQUFFO1lBQ3ZGO1lBRUEsTUFBTUMsZUFBZSxNQUFNVixlQUFlaEMsSUFBSTtZQUM5Q29CLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLDhCQUE4QixDQUFDLEVBQUVXO1lBRTlDLHdEQUF3RDtZQUN4RCxJQUFJQSxhQUFhQyxjQUFjLEVBQUU7Z0JBQy9CLElBQUlELGFBQWFDLGNBQWMsQ0FBQ0MsU0FBUyxFQUFFO29CQUN6Q3RFLGtCQUFrQitDLG9CQUFvQnFCLGFBQWFDLGNBQWMsQ0FBQ0MsU0FBUztvQkFDM0V4QixRQUFRVyxHQUFHLENBQUMsQ0FBQyx1Q0FBdUMsRUFBRVcsYUFBYUMsY0FBYyxDQUFDQyxTQUFTLEVBQUU7Z0JBQy9GO2dCQUNBLElBQUlGLGFBQWFDLGNBQWMsQ0FBQ0UsUUFBUSxFQUFFO29CQUN4Q3JFLGlCQUFpQjZDLG9CQUFvQnFCLGFBQWFDLGNBQWMsQ0FBQ0UsUUFBUTtvQkFDekV6QixRQUFRVyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRVcsYUFBYUMsY0FBYyxDQUFDRSxRQUFRLEVBQUU7Z0JBQzdGO2dCQUNBLElBQUlILGFBQWFDLGNBQWMsQ0FBQ0csTUFBTSxFQUFFO29CQUN0Q3BFLGVBQWUyQyxvQkFBb0JxQixhQUFhQyxjQUFjLENBQUNHLE1BQU07b0JBQ3JFMUIsUUFBUVcsR0FBRyxDQUFDLENBQUMsb0NBQW9DLEVBQUVXLGFBQWFDLGNBQWMsQ0FBQ0csTUFBTSxFQUFFO2dCQUN6RjtnQkFDQSxJQUFJSixhQUFhQyxjQUFjLENBQUNJLElBQUksRUFBRTtvQkFDcENuRSxhQUFheUMsb0JBQW9CcUIsYUFBYUMsY0FBYyxDQUFDSSxJQUFJO29CQUNqRTNCLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLGtDQUFrQyxFQUFFVyxhQUFhQyxjQUFjLENBQUNJLElBQUksRUFBRTtnQkFDckY7WUFDRjtZQUVBL0QsaUJBQWlCO1lBQ2pCb0MsUUFBUVcsR0FBRyxDQUFDLENBQUMsa0RBQWtELEVBQUVqRSxRQUFRO1lBRXpFLGdDQUFnQztZQUNoQ29CLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNmZ0MsUUFBUVcsR0FBRyxDQUFDLENBQUMsa0RBQWtELEVBQUVqRSxRQUFRO1lBRXpFLG1EQUFtRDtZQUNuRCxNQUFNa0YsZUFBZUMsV0FBVztnQkFDOUJ6RCxzQkFBc0I7WUFDeEIsR0FBRztZQUVILE1BQU0wRCxpQkFBaUIsTUFBTXBELE1BQU0sQ0FBQyxnQ0FBZ0MsRUFBRWhDLE9BQU8sYUFBYSxDQUFDLEVBQUU7Z0JBQzNGbUUsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxVQUFVO2dCQUNaO2dCQUNBQyxRQUFRQyxZQUFZQyxPQUFPLENBQUM7WUFDOUI7WUFFQWMsYUFBYUg7WUFFYixJQUFJLENBQUNFLGVBQWVaLEVBQUUsRUFBRTtnQkFDdEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsYUFBYSxFQUFFVyxlQUFlVixNQUFNLENBQUMsRUFBRSxFQUFFVSxlQUFlVCxVQUFVLEVBQUU7WUFDdkY7WUFFQSxNQUFNVyxlQUFlLE1BQU1GLGVBQWVsRCxJQUFJO1lBQzlDb0IsUUFBUVcsR0FBRyxDQUFDLENBQUMsOEJBQThCLENBQUMsRUFBRXFCO1lBRTlDLHlDQUF5QztZQUN6QyxJQUFJQSxhQUFhVCxjQUFjLElBQUlTLGFBQWFULGNBQWMsQ0FBQ1UsTUFBTSxFQUFFO2dCQUNyRXZFLGVBQWV1QyxvQkFBb0IrQixhQUFhVCxjQUFjLENBQUNVLE1BQU07Z0JBQ3JFakMsUUFBUVcsR0FBRyxDQUFDLENBQUMsb0NBQW9DLEVBQUVxQixhQUFhVCxjQUFjLENBQUNVLE1BQU0sRUFBRTtZQUN6RjtZQUVBbkUsaUJBQWlCO1lBQ2pCRSxlQUFlO1lBQ2ZnQyxRQUFRVyxHQUFHLENBQUMsQ0FBQyxrREFBa0QsRUFBRWpFLFFBQVE7WUFFekVzRCxRQUFRVyxHQUFHLENBQUMsQ0FBQyxxREFBcUQsRUFBRWpFLFFBQVE7UUFFOUUsRUFBRSxPQUFPcUQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUUxQyxJQUFJQSxpQkFBaUJvQixPQUFPO2dCQUMxQixJQUFJcEIsTUFBTW1DLElBQUksS0FBSyxnQkFBZ0I7b0JBQ2pDaEUsbUJBQW1CO2dCQUNyQixPQUFPO29CQUNMQSxtQkFBbUIsQ0FBQyxPQUFPLEVBQUU2QixNQUFNb0MsT0FBTyxFQUFFO2dCQUM5QztZQUNGLE9BQU87Z0JBQ0xqRSxtQkFBbUI7WUFDckI7WUFFQSx3Q0FBd0M7WUFDeENoQixrQkFBa0I7WUFDbEJFLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNmRSxhQUFhO1lBQ2JFLGVBQWU7UUFDakIsU0FBVTtZQUNSRSxpQkFBaUI7WUFDakJFLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNmSSxzQkFBc0I7WUFDdEJHLFNBQVNrQyxPQUFPLEdBQUc7UUFDckI7SUFDRjtJQUVBLFVBQVU7SUFDVmxFLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlHLFFBQVE7Z0JBQ1Ysd0RBQXdEO2dCQUN4RDhEO1lBQ0YsT0FBTztnQkFDTCw4Q0FBOEM7Z0JBQzlDaEM7WUFDRjtRQUNGO2lDQUFHO1FBQUM5QjtLQUFPO0lBRVgsMkNBQTJDO0lBQzNDSCxnREFBU0E7a0NBQUM7WUFDUixJQUFJLENBQUNHLFFBQVE7Z0JBQ1gsTUFBTTBGLFdBQVdDO3VEQUFZO3dCQUMzQjdEO29CQUNGO3NEQUFHO2dCQUVIOzhDQUFPLElBQU04RCxjQUFjRjs7WUFDN0I7UUFDRjtpQ0FBRztRQUFDMUY7S0FBTztJQUVYLGtDQUFrQztJQUNsQyxNQUFNNkYsZUFBZSxDQUFDNUM7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUNFLDhEQUFDNkM7b0JBQUlDLFdBQVU7b0JBQXlCQyxNQUFLO29CQUFPQyxRQUFPO29CQUFlQyxTQUFROzhCQUNoRiw0RUFBQ0M7d0JBQUtDLGVBQWM7d0JBQVFDLGdCQUFlO3dCQUFRQyxhQUFhO3dCQUFHQyxHQUFFOzs7Ozs7Ozs7OztZQUczRSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVDtvQkFBSUMsV0FBVTtvQkFBdUJDLE1BQUs7b0JBQU9DLFFBQU87b0JBQWVDLFNBQVE7OEJBQzlFLDRFQUFDQzt3QkFBS0MsZUFBYzt3QkFBUUMsZ0JBQWU7d0JBQVFDLGFBQWE7d0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBRzNFO2dCQUNFLHFCQUNFLDhEQUFDVDtvQkFBSUMsV0FBVTtvQkFBeUJDLE1BQUs7b0JBQU9DLFFBQU87b0JBQWVDLFNBQVE7OEJBQ2hGLDRFQUFDQzt3QkFBS0MsZUFBYzt3QkFBUUMsZ0JBQWU7d0JBQVFDLGFBQWE7d0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1FBRzdFO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ3ZEO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFBTSxPQUFPO1lBQ2xCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNd0QsdUJBQXVCLENBQUMvQjtRQUM1QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1nQyxnQkFBZ0IsQ0FBQ2hDO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFVLE9BQU87WUFDdEI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsMkNBQTJDO0lBQzNDLElBQUkxRSxRQUFRO1FBQ1YscUJBQ0UsOERBQUMyRztZQUFJWixXQUFVOzs4QkFFYiw4REFBQ1k7b0JBQUlaLFdBQVU7O3NDQUNiLDhEQUFDYTs0QkFBR2IsV0FBVTtzQ0FBb0M7Ozs7OztzQ0FDbEQsOERBQUNjOzRCQUFFZCxXQUFVOztnQ0FBMEIvRjtnQ0FBTzs7Ozs7OztzQ0FDOUMsOERBQUMyRzs0QkFBSVosV0FBVTs7Ozs7Ozs7Ozs7OzhCQUlqQiw4REFBQ1k7b0JBQUlaLFdBQVU7OEJBQ2IsNEVBQUNZO3dCQUFJWixXQUFVOzswQ0FFYiw4REFBQ1k7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLENBQUMscUJBQXFCLEVBQ3BDeEYsbUJBQW1CLGFBQWEsZ0JBQWdCa0cscUJBQXFCbEcsZ0JBQ3RFLG1EQUFtRCxFQUNsREEsbUJBQW1CLGFBQWEsS0FBSyxpQkFDckM7Ozs7Ozt3REFDREEsbUJBQW1CLDRCQUNsQiw4REFBQ29HOzREQUFJWixXQUFXLENBQUMsc0NBQXNDLEVBQUVVLHFCQUFxQmxHLGdCQUFnQix3QkFBd0IsQ0FBQzs7Ozs7Ozs7Ozs7OzhEQUczSCw4REFBQ3VHO29EQUFLZixXQUFXLENBQUMsb0JBQW9CLEVBQ3BDeEYsbUJBQW1CLGFBQWEsa0JBQWtCLHVDQUNuRCxrQkFBa0IsQ0FBQzs4REFBRTs7Ozs7Ozs7Ozs7O3NEQUl4Qiw4REFBQ3VHOzRDQUFLZixXQUFXLENBQUMsZ0RBQWdELEVBQ2hFeEYsbUJBQW1CLGFBQWEsa0JBQ2hDQSxtQkFBbUIsU0FBUyxtQkFDNUJBLG1CQUFtQixZQUFZLG9CQUMvQixnQkFDQTtzREFDQ0EsbUJBQW1CLGFBQWEsUUFBUW1HLGNBQWNuRzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTdELDhEQUFDb0c7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLENBQUMscUJBQXFCLEVBQ3BDdEYsa0JBQWtCLGFBQWEsZ0JBQWdCZ0cscUJBQXFCaEcsZUFDckUsbURBQW1ELEVBQ2xEQSxrQkFBa0IsYUFBYSxLQUFLLGlCQUNwQzs7Ozs7O3dEQUNEQSxrQkFBa0IsNEJBQ2pCLDhEQUFDa0c7NERBQUlaLFdBQVcsQ0FBQyxzQ0FBc0MsRUFBRVUscUJBQXFCaEcsZUFBZSx3QkFBd0IsQ0FBQzs7Ozs7Ozs7Ozs7OzhEQUcxSCw4REFBQ3FHO29EQUFLZixXQUFXLENBQUMsb0JBQW9CLEVBQ3BDdEYsa0JBQWtCLGFBQWEsa0JBQWtCLHVDQUNsRCxrQkFBa0IsQ0FBQzs4REFBRTs7Ozs7Ozs7Ozs7O3NEQUl4Qiw4REFBQ3FHOzRDQUFLZixXQUFXLENBQUMsZ0RBQWdELEVBQ2hFdEYsa0JBQWtCLGFBQWEsa0JBQy9CQSxrQkFBa0IsU0FBUyxtQkFDM0JBLGtCQUFrQixZQUFZLG9CQUM5QixnQkFDQTtzREFDQ0Esa0JBQWtCLGFBQWEsUUFBUWlHLGNBQWNqRzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTVELDhEQUFDa0c7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLENBQUMscUJBQXFCLEVBQ3BDcEYsZ0JBQWdCLGFBQWEsZ0JBQWdCOEYscUJBQXFCOUYsYUFDbkUsbURBQW1ELEVBQ2xEQSxnQkFBZ0IsYUFBYSxLQUFLLGlCQUNsQzs7Ozs7O3dEQUNEQSxnQkFBZ0IsNEJBQ2YsOERBQUNnRzs0REFBSVosV0FBVyxDQUFDLHNDQUFzQyxFQUFFVSxxQkFBcUI5RixhQUFhLHdCQUF3QixDQUFDOzs7Ozs7Ozs7Ozs7OERBR3hILDhEQUFDbUc7b0RBQUtmLFdBQVcsQ0FBQyxvQkFBb0IsRUFDcENwRixnQkFBZ0IsYUFBYSxrQkFBa0IsdUNBQ2hELGtCQUFrQixDQUFDOzhEQUFFOzs7Ozs7Ozs7Ozs7c0RBSXhCLDhEQUFDbUc7NENBQUtmLFdBQVcsQ0FBQyxnREFBZ0QsRUFDaEVwRixnQkFBZ0IsYUFBYSxrQkFDN0JBLGdCQUFnQixTQUFTLG1CQUN6QkEsZ0JBQWdCLFlBQVksb0JBQzVCLGdCQUNBO3NEQUNDQSxnQkFBZ0IsYUFBYSxRQUFRK0YsY0FBYy9GOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNMUQsOERBQUNnRzswQ0FDQyw0RUFBQ0E7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDWTs0Q0FBSVosV0FBVTs7OERBQ2IsOERBQUNZO29EQUFJWixXQUFVOztzRUFDYiw4REFBQ1k7NERBQUlaLFdBQVcsQ0FBQyxxQkFBcUIsRUFDcENsRixjQUFjLGFBQWEsZ0JBQWdCNEYscUJBQXFCNUYsV0FDakUsbURBQW1ELEVBQ2xEQSxjQUFjLGFBQWEsS0FBSyxpQkFDaEM7Ozs7Ozt3REFDREEsY0FBYyw0QkFDYiw4REFBQzhGOzREQUFJWixXQUFXLENBQUMsc0NBQXNDLEVBQUVVLHFCQUFxQjVGLFdBQVcsd0JBQXdCLENBQUM7Ozs7Ozs7Ozs7Ozs4REFHdEgsOERBQUNpRztvREFBS2YsV0FBVyxDQUFDLG9CQUFvQixFQUNwQ2xGLGNBQWMsYUFBYSxrQkFBa0IsdUNBQzlDLGtCQUFrQixDQUFDOzhEQUFFOzs7Ozs7Ozs7Ozs7c0RBSXhCLDhEQUFDaUc7NENBQUtmLFdBQVcsQ0FBQyxnREFBZ0QsRUFDaEVsRixjQUFjLGFBQWEsa0JBQzNCQSxjQUFjLFNBQVMsbUJBQ3ZCQSxjQUFjLFlBQVksb0JBQzFCLGdCQUNBO3NEQUNDQSxjQUFjLGFBQWEsUUFBUTZGLGNBQWM3Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXhELDhEQUFDOEY7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLENBQUMscUJBQXFCLEVBQ3BDaEYsZ0JBQWdCLGFBQWEsZ0JBQWdCMEYscUJBQXFCMUYsYUFDbkUsbURBQW1ELEVBQ2xEQSxnQkFBZ0IsYUFBYSxLQUFLLGlCQUNsQzs7Ozs7O3dEQUNEQSxnQkFBZ0IsNEJBQ2YsOERBQUM0Rjs0REFBSVosV0FBVyxDQUFDLHNDQUFzQyxFQUFFVSxxQkFBcUIxRixhQUFhLHdCQUF3QixDQUFDOzs7Ozs7d0RBR3JITSw2QkFDQyw4REFBQ3NGOzREQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7OERBR25CLDhEQUFDZTtvREFBS2YsV0FBVyxDQUFDLG9CQUFvQixFQUNwQ2hGLGdCQUFnQixhQUFhLGtCQUFrQix1Q0FDaEQsa0JBQWtCLENBQUM7OERBQ2pCTSxjQUFjLG9CQUFvQjs7Ozs7Ozs7Ozs7O3NEQUd2Qyw4REFBQ3lGOzRDQUFLZixXQUFXLENBQUMsZ0RBQWdELEVBQ2hFaEYsZ0JBQWdCLGFBQWEsa0JBQzdCQSxnQkFBZ0IsU0FBUyxtQkFDekJBLGdCQUFnQixZQUFZLG9CQUM1QixnQkFDQTtzREFDQ00sY0FBYyxXQUFXTixnQkFBZ0IsYUFBYSxRQUFRMkYsY0FBYzNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVF0RkUsK0JBQ0MsOERBQUMwRjtvQkFBSVosV0FBVTs4QkFDYiw0RUFBQ1k7d0JBQUlaLFdBQVU7OzBDQUNiLDhEQUFDWTtnQ0FBSVosV0FBVTs7Ozs7OzBDQUNmLDhEQUFDZTtnQ0FBS2YsV0FBVTswQ0FBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVE5QzVFLCtCQUNDLDhEQUFDd0Y7b0JBQUlaLFdBQVU7OEJBQ2IsNEVBQUNZO3dCQUFJWixXQUFVOzswQ0FDYiw4REFBQ1k7Z0NBQUlaLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ2U7Z0NBQUtmLFdBQVU7MENBQXlCOzs7Ozs7Ozs7Ozs7Ozs7OztnQkFROUN0RSxvQ0FDQyw4REFBQ2tGO29CQUFJWixXQUFVOzhCQUNiLDRFQUFDWTt3QkFBSVosV0FBVTtrQ0FBc0M7Ozs7Ozs7Ozs7O2dCQU94RHhFLGlDQUNDLDhEQUFDb0Y7b0JBQUlaLFdBQVU7OEJBQ2IsNEVBQUNZO3dCQUFJWixXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Z0NBQVVDLE1BQUs7Z0NBQU9DLFFBQU87Z0NBQWVDLFNBQVE7MENBQ2pFLDRFQUFDQztvQ0FBS0MsZUFBYztvQ0FBUUMsZ0JBQWU7b0NBQVFDLGFBQWE7b0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7OzBDQUV2RSw4REFBQ087O29DQUFLO29DQUFLdkY7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFNaEJBLG1CQUFtQixDQUFDTixpQkFBaUIsQ0FBQ0UsK0JBQ3JDLDhEQUFDd0Y7b0JBQUlaLFdBQVU7OEJBQ2IsNEVBQUNnQjt3QkFDQ0MsU0FBU2xEO3dCQUNUaUMsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFTWDtJQUVBLDZDQUE2QztJQUM3QyxxQkFDRSw4REFBQ1k7UUFBSVosV0FBVTs7MEJBRWIsOERBQUNZO2dCQUFJWixXQUFVOztrQ0FDYiw4REFBQ2E7d0JBQUdiLFdBQVU7a0NBQStCOzs7Ozs7a0NBQzdDLDhEQUFDWTt3QkFBSVosV0FBVyxDQUFDLG1DQUFtQyxFQUFFNUYsVUFBVSxrQkFBa0IsZ0JBQWdCOzs7Ozs7Ozs7Ozs7MEJBSXBHLDhEQUFDd0c7Z0JBQUlaLFdBQVU7MEJBQ1o1RixXQUFXRixXQUFXZ0gsTUFBTSxLQUFLLElBQ2hDLFVBQVU7Z0JBQ1ZDLE1BQU1DLElBQUksQ0FBQztvQkFBRUYsUUFBUTtnQkFBRSxHQUFHM0UsR0FBRyxDQUFDLENBQUM4RSxHQUFHQyxzQkFDaEMsOERBQUNWO3dCQUFnQlosV0FBVTs7MENBQ3pCLDhEQUFDWTtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7MENBRWpCLDhEQUFDWTtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7O3VCQVBUc0I7Ozs7cUNBWVpwSCxXQUFXcUMsR0FBRyxDQUFDLENBQUNnRixXQUFXRCxzQkFDM0IsOERBQUNWO3dCQUVDWixXQUFVOzswQ0FFViw4REFBQ1k7Z0NBQUlaLFdBQVU7O2tEQUNiLDhEQUFDZTt3Q0FBS2YsV0FBVTtrREFBc0N1QixVQUFVOUUsS0FBSzs7Ozs7O29DQUNwRXFELGFBQWF5QixVQUFVckUsS0FBSzs7Ozs7OzswQ0FFL0IsOERBQUMwRDtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNlO3dDQUFLZixXQUFVO2tEQUF3Q3VCLFVBQVU3RSxLQUFLOzs7Ozs7a0RBQ3ZFLDhEQUFDcUU7d0NBQUtmLFdBQVcsQ0FBQyxvQkFBb0IsRUFBRVMsZUFBZWMsVUFBVXJFLEtBQUssR0FBRztrREFDdEVxRSxVQUFVeEUsTUFBTTs7Ozs7Ozs7Ozs7Ozt1QkFWaEJ1RTs7Ozs7Ozs7OzswQkFtQlgsOERBQUNWO2dCQUFJWixXQUFVOzBCQUNiLDRFQUFDWTtvQkFBSVosV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzRCQUFVQyxNQUFLOzRCQUFPQyxRQUFPOzRCQUFlQyxTQUFRO3NDQUNqRSw0RUFBQ0M7Z0NBQUtDLGVBQWM7Z0NBQVFDLGdCQUFlO2dDQUFRQyxhQUFhO2dDQUFHQyxHQUFFOzs7Ozs7Ozs7OztzQ0FFdkUsOERBQUNPO3NDQUNFM0csVUFBVSxjQUNWRSxhQUFhLENBQUMsVUFBVSxFQUFFQSxZQUFZLEdBQ3RDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iO0FBRUEsaUVBQWVOLFlBQVlBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29uZzdcXERlc2t0b3BcXGhvbWVcXHVidW50dVxcZmluYW5jaWFsX2Rhc2hib2FyZFxcc3JjXFxjb21wb25lbnRzXFxTcGVlZFRyYWZmaWMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBNYXJrZXRJbmRpY2F0b3Ige1xuICBsYWJlbDogc3RyaW5nO1xuICB2YWx1ZTogc3RyaW5nO1xuICBjaGFuZ2U6IHN0cmluZztcbiAgdHJlbmQ6ICd1cCcgfCAnZG93bicgfCAnbmV1dHJhbCc7XG4gIGNvbG9yOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBTcGVlZFRyYWZmaWNQcm9wcyB7XG4gIHN5bWJvbD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIExTVE1SZXN1bHQge1xuICBzeW1ib2w6IHN0cmluZztcbiAgdHJhaW5fdW50aWw6IHN0cmluZztcbiAgcHJlZGljdGlvbnM6IEFycmF5PHtcbiAgICBkYXRlOiBzdHJpbmc7XG4gICAgcHJlZF9wcm9iX3VwOiBudW1iZXI7XG4gICAgcHJlZGljdGVkX2xhYmVsOiBudW1iZXI7XG4gICAgYWN0dWFsX2xhYmVsOiBudW1iZXI7XG4gICAgcmVzdWx0X2NvbG9yOiBzdHJpbmc7XG4gIH0+O1xuICBzdW1tYXJ5X2tvPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgTUZJUmVzdWx0IHtcbiAgc3ltYm9sOiBzdHJpbmc7XG4gIGRhdGU6IHN0cmluZztcbiAgbWZpXzE0OiBudW1iZXI7XG4gIHRyYWZmaWNfbGlnaHQ6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFByZWRpY3Rpb25SZXN1bHQge1xuICBwaGFzZT86IG51bWJlcjtcbiAgbHN0bT86IExTVE1SZXN1bHQ7XG4gIG1maT86IE1GSVJlc3VsdDtcbiAgYm9sbGluZ2VyPzogYW55O1xuICByc2k/OiBhbnk7XG4gIGluZHVzdHJ5PzogYW55O1xuICBjYXBtPzogYW55O1xuICBnYXJjaD86IGFueTtcbiAgdHJhZmZpY19saWdodHM/OiB7XG4gICAgdGVjaG5pY2FsPzogc3RyaW5nO1xuICAgIGluZHVzdHJ5Pzogc3RyaW5nO1xuICAgIG1hcmtldD86IHN0cmluZztcbiAgICByaXNrPzogc3RyaW5nO1xuICAgIG5ldXJhbD86IHN0cmluZztcbiAgfTtcbn1cblxuY29uc3QgU3BlZWRUcmFmZmljOiBSZWFjdC5GQzxTcGVlZFRyYWZmaWNQcm9wcz4gPSAoeyBzeW1ib2wgfSkgPT4ge1xuICAvLyBNYXJrZXQgaW5kaWNhdG9ycyBzdGF0ZSAoUHJlLXRpY2tlciBtb2RlKVxuICBjb25zdCBbaW5kaWNhdG9ycywgc2V0SW5kaWNhdG9yc10gPSB1c2VTdGF0ZTxNYXJrZXRJbmRpY2F0b3JbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2xhc3RVcGRhdGUsIHNldExhc3RVcGRhdGVdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG5cbiAgLy8gVHJhZmZpYyBsaWdodHMgc3RhdGUgKFBvc3QtdGlja2VyIG1vZGUpIC0gTmV3IG9yZGVyXG4gIGNvbnN0IFt0ZWNobmljYWxMaWdodCwgc2V0VGVjaG5pY2FsTGlnaHRdID0gdXNlU3RhdGU8J2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnPignaW5hY3RpdmUnKTsgLy8gTGlnaHQgMTogVGVjaG5pY2FsIEFuYWx5c2lzXG4gIGNvbnN0IFtpbmR1c3RyeUxpZ2h0LCBzZXRJbmR1c3RyeUxpZ2h0XSA9IHVzZVN0YXRlPCdnb29kJyB8ICd3YXJuaW5nJyB8ICdkYW5nZXInIHwgJ2luYWN0aXZlJz4oJ2luYWN0aXZlJyk7IC8vIExpZ2h0IDI6IEluZHVzdHJ5IFNlbnNpdGl2aXR5XG4gIGNvbnN0IFttYXJrZXRMaWdodCwgc2V0TWFya2V0TGlnaHRdID0gdXNlU3RhdGU8J2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnPignaW5hY3RpdmUnKTsgLy8gTGlnaHQgMzogTWFya2V0IFNlbnNpdGl2aXR5IChDQVBNKVxuICBjb25zdCBbcmlza0xpZ2h0LCBzZXRSaXNrTGlnaHRdID0gdXNlU3RhdGU8J2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnPignaW5hY3RpdmUnKTsgLy8gTGlnaHQgNDogVm9sYXRpbGl0eSBSaXNrXG4gIGNvbnN0IFtuZXVyYWxMaWdodCwgc2V0TmV1cmFsTGlnaHRdID0gdXNlU3RhdGU8J2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnPignaW5hY3RpdmUnKTsgLy8gTGlnaHQgNTogTmV1cmFsIE5ldHdvcmsgKExTVE0pXG5cbiAgLy8gUHJlZGljdGlvbiBzdGF0ZVxuICBjb25zdCBbcGhhc2UxTG9hZGluZywgc2V0UGhhc2UxTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtwaGFzZTJMb2FkaW5nLCBzZXRQaGFzZTJMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xzdG1Mb2FkaW5nLCBzZXRMc3RtTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtwcmVkaWN0aW9uRXJyb3IsIHNldFByZWRpY3Rpb25FcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Nob3dUaW1lb3V0TWVzc2FnZSwgc2V0U2hvd1RpbWVvdXRNZXNzYWdlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xhc3RSZXF1ZXN0VGltZSwgc2V0TGFzdFJlcXVlc3RUaW1lXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XG5cbiAgLy8gU2luZ2xlLWZsaWdodCBndWFyZFxuICBjb25zdCBpbkZsaWdodCA9IHVzZVJlZihmYWxzZSk7XG5cbiAgLy8g7Iuk7KCcIOyLnOyepSDrjbDsnbTthLAg6rCA7KC47Jik6riwIChQcmUtdGlja2VyIG1vZGUpXG4gIGNvbnN0IGZldGNoTWFya2V0RGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvbWFya2V0X2RhdGEnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5kYXRhKSB7XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSByZXN1bHQuZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4gKHtcbiAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCxcbiAgICAgICAgICB2YWx1ZTogaXRlbS5wcmljZS50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsXG4gICAgICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDJcbiAgICAgICAgICB9KSxcbiAgICAgICAgICBjaGFuZ2U6IGAke2l0ZW0uY2hhbmdlUGVyY2VudCA+PSAwID8gJysnIDogJyd9JHtpdGVtLmNoYW5nZVBlcmNlbnQudG9GaXhlZCgyKX0lYCxcbiAgICAgICAgICB0cmVuZDogaXRlbS50cmVuZCxcbiAgICAgICAgICBjb2xvcjogaXRlbS50cmVuZCA9PT0gJ3VwJyA/ICdncmVlbicgOiBpdGVtLnRyZW5kID09PSAnZG93bicgPyAncmVkJyA6ICd5ZWxsb3cnXG4gICAgICAgIH0pKTtcblxuICAgICAgICBzZXRJbmRpY2F0b3JzKGZvcm1hdHRlZERhdGEpO1xuICAgICAgICBzZXRMYXN0VXBkYXRlKG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCdrby1LUicpKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIG1hcmtldCBkYXRhOicsIGVycm9yKTtcbiAgICAgIC8vIOyXkOufrCDsi5wgZmFsbGJhY2sg642w7J207YSwIOyCrOyaqVxuICAgICAgc2V0SW5kaWNhdG9ycyhbXG4gICAgICAgIHsgbGFiZWw6ICdTJlAgNTAwJywgdmFsdWU6ICc0LDU2Ny44OScsIGNoYW5nZTogJysxLjIlJywgdHJlbmQ6ICd1cCcsIGNvbG9yOiAnZ3JlZW4nIH0sXG4gICAgICAgIHsgbGFiZWw6ICfrgpjsiqTri6UnLCB2YWx1ZTogJzE0LDIzNC41NicsIGNoYW5nZTogJyswLjglJywgdHJlbmQ6ICd1cCcsIGNvbG9yOiAnZ3JlZW4nIH0sXG4gICAgICAgIHsgbGFiZWw6ICfri6TsmrDsobTsiqQnLCB2YWx1ZTogJzM0LDU2Ny4xMicsIGNoYW5nZTogJy0wLjMlJywgdHJlbmQ6ICdkb3duJywgY29sb3I6ICdyZWQnIH0sXG4gICAgICAgIHsgbGFiZWw6ICdWSVgnLCB2YWx1ZTogJzE4LjQ1JywgY2hhbmdlOiAnLTIuMSUnLCB0cmVuZDogJ2Rvd24nLCBjb2xvcjogJ3llbGxvdycgfSxcbiAgICAgICAgeyBsYWJlbDogJ+uLrOufrC/sm5AnLCB2YWx1ZTogJzEsMzI3LjUwJywgY2hhbmdlOiAnKzAuNSUnLCB0cmVuZDogJ3VwJywgY29sb3I6ICdncmVlbicgfSxcbiAgICAgIF0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ29udmVydCByZXN1bHQgY29sb3IgdG8gdHJhZmZpYyBsaWdodCBzdGF0dXNcbiAgY29uc3QgcmVzdWx0Q29sb3JUb1N0YXR1cyA9IChjb2xvcjogc3RyaW5nKTogJ2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgPT4ge1xuICAgIHN3aXRjaCAoY29sb3I/LnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgIGNhc2UgJ2dyZWVuJzpcbiAgICAgIGNhc2UgJ2dvb2QnOlxuICAgICAgICByZXR1cm4gJ2dvb2QnO1xuICAgICAgY2FzZSAneWVsbG93JzpcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ3dhcm5pbmcnO1xuICAgICAgY2FzZSAncmVkJzpcbiAgICAgIGNhc2UgJ2Rhbmdlcic6XG4gICAgICAgIHJldHVybiAnZGFuZ2VyJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnd2FybmluZyc7XG4gICAgfVxuICB9O1xuXG4gIC8vIEV4dHJhY3QgY29sb3IgZnJvbSBMU1RNL01GSSByZXN1bHRzXG4gIGNvbnN0IGdldENvbG9yRnJvbVJlc3VsdCA9IChvYmo6IGFueSk6IHN0cmluZyB8IHVuZGVmaW5lZCA9PiB7XG4gICAgaWYgKCFvYmopIHJldHVybiB1bmRlZmluZWQ7XG4gICAgaWYgKHR5cGVvZiBvYmogPT09ICdzdHJpbmcnKSByZXR1cm4gb2JqO1xuICAgIGlmIChvYmoucmVzdWx0X2NvbG9yKSByZXR1cm4gb2JqLnJlc3VsdF9jb2xvcjtcbiAgICBpZiAob2JqLnRyYWZmaWNfbGlnaHQpIHJldHVybiBvYmoudHJhZmZpY19saWdodDtcbiAgICBpZiAob2JqLmNvbG9yKSByZXR1cm4gb2JqLmNvbG9yO1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH07XG5cbiAgLy8gU3RhZ2VkIGV4ZWN1dGlvbjogUGhhc2UgMSAoRmFzdCBzZXJ2aWNlcykgKyBQaGFzZSAyIChMU1RNKVxuICBjb25zdCBmZXRjaFN0YWdlZFByZWRpY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzeW1ib2wgfHwgaW5GbGlnaHQuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgLy8gUHJldmVudCB0b28gZnJlcXVlbnQgcmVxdWVzdHMgKG1pbmltdW0gMTAgc2Vjb25kcyBiZXR3ZWVuIHJlcXVlc3RzKVxuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgaWYgKG5vdyAtIGxhc3RSZXF1ZXN0VGltZSA8IDEwMDAwKSB7XG4gICAgICBjb25zb2xlLmxvZygnUHJlZGljdGlvbiByZXF1ZXN0IHRocm90dGxlZCAtIHRvbyBmcmVxdWVudCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBTZXQgc2luZ2xlLWZsaWdodCBndWFyZFxuICAgICAgaW5GbGlnaHQuY3VycmVudCA9IHRydWU7XG4gICAgICBzZXRMYXN0UmVxdWVzdFRpbWUobm93KTtcblxuICAgICAgLy8gUmVzZXQgYWxsIGxpZ2h0cyB0byBpbmFjdGl2ZSBzdGF0ZVxuICAgICAgc2V0VGVjaG5pY2FsTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRJbmR1c3RyeUxpZ2h0KCdpbmFjdGl2ZScpO1xuICAgICAgc2V0TWFya2V0TGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRSaXNrTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXROZXVyYWxMaWdodCgnaW5hY3RpdmUnKTtcblxuICAgICAgc2V0UHJlZGljdGlvbkVycm9yKG51bGwpO1xuICAgICAgc2V0U2hvd1RpbWVvdXRNZXNzYWdlKGZhbHNlKTtcblxuICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFN0YXJ0aW5nIHN0YWdlZCBwcmVkaWN0aW9uIGZvciAke3N5bWJvbH1gKTtcblxuICAgICAgLy8gUGhhc2UgMTogRXhlY3V0ZSBmYXN0IHNlcnZpY2VzIChUZWNobmljYWwsIEluZHVzdHJ5LCBNYXJrZXQsIFZvbGF0aWxpdHkpXG4gICAgICBzZXRQaGFzZTFMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFBoYXNlIDE6IFN0YXJ0aW5nIGZhc3Qgc2VydmljZXMgZm9yICR7c3ltYm9sfWApO1xuXG4gICAgICBjb25zdCBwaGFzZTFSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3NwZWVkdHJhZmZpY19zdGFnZWQ/c3ltYm9sPSR7c3ltYm9sfSZzdGFnZT1waGFzZTFgLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBzaWduYWw6IEFib3J0U2lnbmFsLnRpbWVvdXQoMzAwMDApLCAvLyAzMCBzZWNvbmQgdGltZW91dCBmb3IgZmFzdCBzZXJ2aWNlc1xuICAgICAgfSk7XG5cbiAgICAgIGlmICghcGhhc2UxUmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBQaGFzZSAxIEhUVFAgJHtwaGFzZTFSZXNwb25zZS5zdGF0dXN9OiAke3BoYXNlMVJlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHBoYXNlMVJlc3VsdCA9IGF3YWl0IHBoYXNlMVJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBQaGFzZSAxIHJlc3VsdDpgLCBwaGFzZTFSZXN1bHQpO1xuXG4gICAgICAvLyBVcGRhdGUgbGlnaHRzIDEtNCBpbW1lZGlhdGVseSBhZnRlciBQaGFzZSAxIGNvbXBsZXRlc1xuICAgICAgaWYgKHBoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cykge1xuICAgICAgICBpZiAocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnRlY2huaWNhbCkge1xuICAgICAgICAgIHNldFRlY2huaWNhbExpZ2h0KHJlc3VsdENvbG9yVG9TdGF0dXMocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnRlY2huaWNhbCkpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBUZWNobmljYWwgbGlnaHQgc2V0IHRvOiAke3BoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy50ZWNobmljYWx9YCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHBoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy5pbmR1c3RyeSkge1xuICAgICAgICAgIHNldEluZHVzdHJ5TGlnaHQocmVzdWx0Q29sb3JUb1N0YXR1cyhwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMuaW5kdXN0cnkpKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gSW5kdXN0cnkgbGlnaHQgc2V0IHRvOiAke3BoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy5pbmR1c3RyeX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLm1hcmtldCkge1xuICAgICAgICAgIHNldE1hcmtldExpZ2h0KHJlc3VsdENvbG9yVG9TdGF0dXMocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLm1hcmtldCkpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBNYXJrZXQgbGlnaHQgc2V0IHRvOiAke3BoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy5tYXJrZXR9YCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHBoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy5yaXNrKSB7XG4gICAgICAgICAgc2V0Umlza0xpZ2h0KHJlc3VsdENvbG9yVG9TdGF0dXMocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnJpc2spKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gUmlzayBsaWdodCBzZXQgdG86ICR7cGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnJpc2t9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgc2V0UGhhc2UxTG9hZGluZyhmYWxzZSk7XG4gICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gUGhhc2UgMSBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IGZvciAke3N5bWJvbH1gKTtcblxuICAgICAgLy8gUGhhc2UgMjogRXhlY3V0ZSBMU1RNIHNlcnZpY2VcbiAgICAgIHNldFBoYXNlMkxvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRMc3RtTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBQaGFzZSAyOiBTdGFydGluZyBMU1RNIHNlcnZpY2UgZm9yICR7c3ltYm9sfWApO1xuXG4gICAgICAvLyBTdGFydCAyMC1zZWNvbmQgdGltZXIgZm9yIEtvcmVhbiB0aW1lb3V0IG1lc3NhZ2VcbiAgICAgIGNvbnN0IHRpbWVvdXRUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRTaG93VGltZW91dE1lc3NhZ2UodHJ1ZSk7XG4gICAgICB9LCAyMDAwMCk7XG5cbiAgICAgIGNvbnN0IHBoYXNlMlJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvc3BlZWR0cmFmZmljX3N0YWdlZD9zeW1ib2w9JHtzeW1ib2x9JnN0YWdlPXBoYXNlMmAsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCg2MDAwMCksIC8vIDYwIHNlY29uZCB0aW1lb3V0IGZvciBMU1RNXG4gICAgICB9KTtcblxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRUaW1lcik7XG5cbiAgICAgIGlmICghcGhhc2UyUmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBQaGFzZSAyIEhUVFAgJHtwaGFzZTJSZXNwb25zZS5zdGF0dXN9OiAke3BoYXNlMlJlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHBoYXNlMlJlc3VsdCA9IGF3YWl0IHBoYXNlMlJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBQaGFzZSAyIHJlc3VsdDpgLCBwaGFzZTJSZXN1bHQpO1xuXG4gICAgICAvLyBVcGRhdGUgbGlnaHQgNSBhZnRlciBQaGFzZSAyIGNvbXBsZXRlc1xuICAgICAgaWYgKHBoYXNlMlJlc3VsdC50cmFmZmljX2xpZ2h0cyAmJiBwaGFzZTJSZXN1bHQudHJhZmZpY19saWdodHMubmV1cmFsKSB7XG4gICAgICAgIHNldE5ldXJhbExpZ2h0KHJlc3VsdENvbG9yVG9TdGF0dXMocGhhc2UyUmVzdWx0LnRyYWZmaWNfbGlnaHRzLm5ldXJhbCkpO1xuICAgICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gTmV1cmFsIGxpZ2h0IHNldCB0bzogJHtwaGFzZTJSZXN1bHQudHJhZmZpY19saWdodHMubmV1cmFsfWApO1xuICAgICAgfVxuXG4gICAgICBzZXRQaGFzZTJMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldExzdG1Mb2FkaW5nKGZhbHNlKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBQaGFzZSAyIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkgZm9yICR7c3ltYm9sfWApO1xuXG4gICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gQWxsIHBoYXNlcyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IGZvciAke3N5bWJvbH1gKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTdGFnZWQgcHJlZGljdGlvbiBlcnJvcjonLCBlcnJvcik7XG5cbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5uYW1lID09PSAnVGltZW91dEVycm9yJykge1xuICAgICAgICAgIHNldFByZWRpY3Rpb25FcnJvcign7JqU7LKtIOyLnOqwhCDstIjqs7wnKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzZXRQcmVkaWN0aW9uRXJyb3IoYOyYiOy4oSDsi6TtjKg6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0UHJlZGljdGlvbkVycm9yKCfsmIjsuKEg7ISc67mE7IqkIOyXsOqysCDsi6TtjKgnKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmVzZXQgYWxsIGxpZ2h0cyB0byBpbmFjdGl2ZSBvbiBlcnJvclxuICAgICAgc2V0VGVjaG5pY2FsTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRJbmR1c3RyeUxpZ2h0KCdpbmFjdGl2ZScpO1xuICAgICAgc2V0TWFya2V0TGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRSaXNrTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXROZXVyYWxMaWdodCgnaW5hY3RpdmUnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0UGhhc2UxTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRQaGFzZTJMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldExzdG1Mb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldFNob3dUaW1lb3V0TWVzc2FnZShmYWxzZSk7XG4gICAgICBpbkZsaWdodC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfVxuICB9O1xuXG4gIC8vIEVmZmVjdHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3ltYm9sKSB7XG4gICAgICAvLyBXaGVuIHN5bWJvbCBpcyBwcm92aWRlZCwgZmV0Y2ggc3RhZ2VkIHByZWRpY3Rpb24gb25jZVxuICAgICAgZmV0Y2hTdGFnZWRQcmVkaWN0aW9uKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFdoZW4gbm8gc3ltYm9sLCBmZXRjaCBtYXJrZXQgZGF0YSBpbml0aWFsbHlcbiAgICAgIGZldGNoTWFya2V0RGF0YSgpO1xuICAgIH1cbiAgfSwgW3N5bWJvbF0pO1xuXG4gIC8vIDIw7LSI66eI64ukIOyLnOyepSDrjbDsnbTthLAg7JeF642w7J207Yq4IChQcmUtdGlja2VyIG1vZGUgb25seSlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXN5bWJvbCkge1xuICAgICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICAgIGZldGNoTWFya2V0RGF0YSgpO1xuICAgICAgfSwgMjAwMDApO1xuXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfVxuICB9LCBbc3ltYm9sXSk7XG5cbiAgLy8gVXRpbGl0eSBmdW5jdGlvbnMgZm9yIHJlbmRlcmluZ1xuICBjb25zdCBnZXRUcmVuZEljb24gPSAodHJlbmQ6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHJlbmQpIHtcbiAgICAgIGNhc2UgJ3VwJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi01MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk03IDE3bDkuMi05LjJNMTcgMTdWN0g3XCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2Rvd24nOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC01MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNyA3bC05LjIgOS4yTTcgN3YxMGgxMFwiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXNsYXRlLTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTIwIDEySDRcIiAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRDaGFuZ2VDb2xvciA9ICh0cmVuZDogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoICh0cmVuZCkge1xuICAgICAgY2FzZSAndXAnOiByZXR1cm4gJ3RleHQtZ3JlZW4tNjAwJztcbiAgICAgIGNhc2UgJ2Rvd24nOiByZXR1cm4gJ3RleHQtcmVkLTYwMCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtc2xhdGUtNTAwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0VHJhZmZpY0xpZ2h0Q29sb3IgPSAoc3RhdHVzOiAnZ29vZCcgfCAnd2FybmluZycgfCAnZGFuZ2VyJykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdnb29kJzogcmV0dXJuICdiZy1ncmVlbi01MDAnO1xuICAgICAgY2FzZSAnd2FybmluZyc6IHJldHVybiAnYmcteWVsbG93LTUwMCc7XG4gICAgICBjYXNlICdkYW5nZXInOiByZXR1cm4gJ2JnLXJlZC01MDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdiZy1ncmF5LTQwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFN0YXR1c1RleHQgPSAoc3RhdHVzOiAnZ29vZCcgfCAnd2FybmluZycgfCAnZGFuZ2VyJykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdnb29kJzogcmV0dXJuICfslpHtmLgnO1xuICAgICAgY2FzZSAnd2FybmluZyc6IHJldHVybiAn67O07Ya1JztcbiAgICAgIGNhc2UgJ2Rhbmdlcic6IHJldHVybiAn7KO87J2YJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAn67aE7ISd7KSRJztcbiAgICB9XG4gIH07XG5cbiAgLy8gUG9zdC10aWNrZXIgbW9kZTogVHJhZmZpYyBsaWdodHMgZGlzcGxheVxuICBpZiAoc3ltYm9sKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IG1heC13LWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiDtl6TrjZQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMCBtYi0xXCI+U3BlZWRUcmFmZmlj4oSiPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNjAwXCI+e3N5bWJvbH0g7Yis7J6QIOyggeqyqeyEsTwvcD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgbXgtYXV0byBtdC0yXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiA1LUxpZ2h0IOyLoO2YuOuTsSDsi5zsiqTthZwgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS04MDAgdG8tc2xhdGUtOTAwIHJvdW5kZWQtMnhsIHAtNiBzaGFkb3cteGwgYm9yZGVyIGJvcmRlci1zbGF0ZS03MDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgey8qIExpZ2h0IDE6IFRlY2huaWNhbCBBbmFseXNpcyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTYgaC02IHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgIHRlY2huaWNhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ2JnLWdyYXktNTAwJyA6IGdldFRyYWZmaWNMaWdodENvbG9yKHRlY2huaWNhbExpZ2h0KVxuICAgICAgICAgICAgICAgICAgICB9IHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi03MDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgICAgICAgICB0ZWNobmljYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICcnIDogJ2FuaW1hdGUtcHVsc2UnXG4gICAgICAgICAgICAgICAgICAgIH1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge3RlY2huaWNhbExpZ2h0ICE9PSAnaW5hY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtnZXRUcmFmZmljTGlnaHRDb2xvcih0ZWNobmljYWxMaWdodCl9IG9wYWNpdHktMjAgYW5pbWF0ZS1waW5nYH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgdGVjaG5pY2FsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOiAndGV4dC13aGl0ZSBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtMzAwJ1xuICAgICAgICAgICAgICAgICAgfSB0cmFuc2l0aW9uLWNvbG9yc2B9PlxuICAgICAgICAgICAgICAgICAgICDquLDsiKDsoIEg67aE7ISdXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgZm9udC1tZWRpdW0gd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgIHRlY2huaWNhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDpcbiAgICAgICAgICAgICAgICAgIHRlY2huaWNhbExpZ2h0ID09PSAnZ29vZCcgPyAndGV4dC1ncmVlbi0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIHRlY2huaWNhbExpZ2h0ID09PSAnd2FybmluZycgPyAndGV4dC15ZWxsb3ctMzAwJyA6XG4gICAgICAgICAgICAgICAgICAndGV4dC1yZWQtMzAwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIHt0ZWNobmljYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICfrjIDquLDspJEnIDogZ2V0U3RhdHVzVGV4dCh0ZWNobmljYWxMaWdodCl9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTGlnaHQgMjogSW5kdXN0cnkgU2Vuc2l0aXZpdHkgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBncm91cFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICBpbmR1c3RyeUxpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ2JnLWdyYXktNTAwJyA6IGdldFRyYWZmaWNMaWdodENvbG9yKGluZHVzdHJ5TGlnaHQpXG4gICAgICAgICAgICAgICAgICAgIH0gc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTcwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgICAgICAgICAgIGluZHVzdHJ5TGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnJyA6ICdhbmltYXRlLXB1bHNlJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtpbmR1c3RyeUxpZ2h0ICE9PSAnaW5hY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtnZXRUcmFmZmljTGlnaHRDb2xvcihpbmR1c3RyeUxpZ2h0KX0gb3BhY2l0eS0yMCBhbmltYXRlLXBpbmdgfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICBpbmR1c3RyeUxpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDogJ3RleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTMwMCdcbiAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbi1jb2xvcnNgfT5cbiAgICAgICAgICAgICAgICAgICAg7IKw7JeFIOuvvOqwkOuPhFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICBpbmR1c3RyeUxpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDpcbiAgICAgICAgICAgICAgICAgIGluZHVzdHJ5TGlnaHQgPT09ICdnb29kJyA/ICd0ZXh0LWdyZWVuLTMwMCcgOlxuICAgICAgICAgICAgICAgICAgaW5kdXN0cnlMaWdodCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTMwMCcgOlxuICAgICAgICAgICAgICAgICAgJ3RleHQtcmVkLTMwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7aW5kdXN0cnlMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICfrjIDquLDspJEnIDogZ2V0U3RhdHVzVGV4dChpbmR1c3RyeUxpZ2h0KX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBMaWdodCAzOiBNYXJrZXQgU2Vuc2l0aXZpdHkgKENBUE0pICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNiBoLTYgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgbWFya2V0TGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnYmctZ3JheS01MDAnIDogZ2V0VHJhZmZpY0xpZ2h0Q29sb3IobWFya2V0TGlnaHQpXG4gICAgICAgICAgICAgICAgICAgIH0gc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTcwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgICAgICAgICAgIG1hcmtldExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJycgOiAnYW5pbWF0ZS1wdWxzZSdcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7bWFya2V0TGlnaHQgIT09ICdpbmFjdGl2ZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCB3LTYgaC02IHJvdW5kZWQtZnVsbCAke2dldFRyYWZmaWNMaWdodENvbG9yKG1hcmtldExpZ2h0KX0gb3BhY2l0eS0yMCBhbmltYXRlLXBpbmdgfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICBtYXJrZXRMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6ICd0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICB9IHRyYW5zaXRpb24tY29sb3JzYH0+XG4gICAgICAgICAgICAgICAgICAgIOyLnOyepSDrr7zqsJDrj4RcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTIgcHktMSBmb250LW1lZGl1bSB3aGl0ZXNwYWNlLW5vd3JhcCAke1xuICAgICAgICAgICAgICAgICAgbWFya2V0TGlnaHQgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOlxuICAgICAgICAgICAgICAgICAgbWFya2V0TGlnaHQgPT09ICdnb29kJyA/ICd0ZXh0LWdyZWVuLTMwMCcgOlxuICAgICAgICAgICAgICAgICAgbWFya2V0TGlnaHQgPT09ICd3YXJuaW5nJyA/ICd0ZXh0LXllbGxvdy0zMDAnIDpcbiAgICAgICAgICAgICAgICAgICd0ZXh0LXJlZC0zMDAnXG4gICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAge21hcmtldExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ+uMgOq4sOykkScgOiBnZXRTdGF0dXNUZXh0KG1hcmtldExpZ2h0KX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBMaWdodCA0OiBWb2xhdGlsaXR5IFJpc2sgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBncm91cFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICByaXNrTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnYmctZ3JheS01MDAnIDogZ2V0VHJhZmZpY0xpZ2h0Q29sb3Iocmlza0xpZ2h0KVxuICAgICAgICAgICAgICAgICAgICB9IHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi03MDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgICAgICAgICByaXNrTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnJyA6ICdhbmltYXRlLXB1bHNlJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtyaXNrTGlnaHQgIT09ICdpbmFjdGl2ZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCB3LTYgaC02IHJvdW5kZWQtZnVsbCAke2dldFRyYWZmaWNMaWdodENvbG9yKHJpc2tMaWdodCl9IG9wYWNpdHktMjAgYW5pbWF0ZS1waW5nYH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgcmlza0xpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDogJ3RleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTMwMCdcbiAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbi1jb2xvcnNgfT5cbiAgICAgICAgICAgICAgICAgICAg67OA64+Z7ISxIOumrOyKpO2BrFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICByaXNrTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOlxuICAgICAgICAgICAgICAgICAgcmlza0xpZ2h0ID09PSAnZ29vZCcgPyAndGV4dC1ncmVlbi0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIHJpc2tMaWdodCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTMwMCcgOlxuICAgICAgICAgICAgICAgICAgJ3RleHQtcmVkLTMwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7cmlza0xpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ+uMgOq4sOykkScgOiBnZXRTdGF0dXNUZXh0KHJpc2tMaWdodCl9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTGlnaHQgNTogTmV1cmFsIE5ldHdvcmsgUHJlZGljdGlvbiAoTFNUTSkgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBncm91cFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICBuZXVyYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICdiZy1ncmF5LTUwMCcgOiBnZXRUcmFmZmljTGlnaHRDb2xvcihuZXVyYWxMaWdodClcbiAgICAgICAgICAgICAgICAgICAgfSBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNzAwIGVhc2UtaW4tb3V0ICR7XG4gICAgICAgICAgICAgICAgICAgICAgbmV1cmFsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnJyA6ICdhbmltYXRlLXB1bHNlJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtuZXVyYWxMaWdodCAhPT0gJ2luYWN0aXZlJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIHctNiBoLTYgcm91bmRlZC1mdWxsICR7Z2V0VHJhZmZpY0xpZ2h0Q29sb3IobmV1cmFsTGlnaHQpfSBvcGFjaXR5LTIwIGFuaW1hdGUtcGluZ2B9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7LyogTFNUTSBMb2FkaW5nIFNwaW5uZXIgKi99XG4gICAgICAgICAgICAgICAgICAgIHtsc3RtTG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHctNiBoLTYgcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ibHVlLTUwMCBib3JkZXItdC10cmFuc3BhcmVudCBhbmltYXRlLXNwaW5cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICBuZXVyYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6ICd0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICB9IHRyYW5zaXRpb24tY29sb3JzYH0+XG4gICAgICAgICAgICAgICAgICAgIHtsc3RtTG9hZGluZyA/ICfrlKXrn6zri50g6riw67CYIOyYiOy4oShMU1RNKScgOiAn7Iug6rK966edIOyYiOy4oShMU1RNKSd9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgZm9udC1tZWRpdW0gd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgIG5ldXJhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDpcbiAgICAgICAgICAgICAgICAgIG5ldXJhbExpZ2h0ID09PSAnZ29vZCcgPyAndGV4dC1ncmVlbi0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIG5ldXJhbExpZ2h0ID09PSAnd2FybmluZycgPyAndGV4dC15ZWxsb3ctMzAwJyA6XG4gICAgICAgICAgICAgICAgICAndGV4dC1yZWQtMzAwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIHtsc3RtTG9hZGluZyA/ICfrtoTshJ3spJEuLi4nIDogbmV1cmFsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAn64yA6riw7KSRJyA6IGdldFN0YXR1c1RleHQobmV1cmFsTGlnaHQpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBoYXNlIDEgTG9hZGluZyBTdGF0ZSAqL31cbiAgICAgICAge3BoYXNlMUxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItMiBib3JkZXItb3JhbmdlLTUwMCBib3JkZXItdC10cmFuc3BhcmVudFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNjAwXCI+XG4gICAgICAgICAgICAgICAg6riw7Iig7KCBIOu2hOyEnSwg7IKw7JeF6rWwLCDsi5zsnqUg66+86rCQ64+ELCDrs4Drj5nshLEg7JyE7ZeYIOu2hOyEnSDspJEuLi5cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFBoYXNlIDIgTG9hZGluZyBTdGF0ZSAqL31cbiAgICAgICAge3BoYXNlMkxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItMiBib3JkZXItYmx1ZS01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgICAgICAgIOuUpeufrOuLnSDquLDrsJgg7JiI7LihKExTVE0pIOu2hOyEnSDspJEuLi5cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEtvcmVhbiBUaW1lb3V0IE1lc3NhZ2UgKi99XG4gICAgICAgIHtzaG93VGltZW91dE1lc3NhZ2UgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwMC8xMCBib3JkZXIgYm9yZGVyLXllbGxvdy01MDAvMjAgcm91bmRlZC1sZyBwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy02MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAg7J20IOyekeyXheydgCDsi5zqsITsnbQg6rG466a964uI64ukLi4uICjstZzrjIAgNjDstIgpXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogRXJyb3IgRGlzcGxheSAqL31cbiAgICAgICAge3ByZWRpY3Rpb25FcnJvciAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAwLzEwIGJvcmRlciBib3JkZXItcmVkLTUwMC8yMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LXJlZC02MDBcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItMi41TDEzLjczMiA0Yy0uNzctLjgzMy0xLjk2NC0uODMzLTIuNzMyIDBMMy43MzIgMTYuNWMtLjc3LjgzMy4xOTIgMi41IDEuNzMyIDIuNXpcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHNwYW4+7Jik66WYOiB7cHJlZGljdGlvbkVycm9yfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBSZXRyeSBCdXR0b24gKi99XG4gICAgICAgIHtwcmVkaWN0aW9uRXJyb3IgJiYgIXBoYXNlMUxvYWRpbmcgJiYgIXBoYXNlMkxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hTdGFnZWRQcmVkaWN0aW9ufVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRleHQtc20gaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDri6Tsi5wg7Iuc64+EXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuXG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gUHJlLXRpY2tlciBtb2RlOiBNYXJrZXQgaW5kaWNhdG9ycyBkaXNwbGF5XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIHsvKiDtl6TrjZQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMFwiPuyLnOyepSDtmITtmak8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgJHtsb2FkaW5nID8gJ2JnLXllbGxvdy00MDAnIDogJ2JnLWdyZWVuLTQwMCd9YH0+PC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOyngO2RnCDrqqnroZ0gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICB7bG9hZGluZyAmJiBpbmRpY2F0b3JzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAvLyDroZzrlKkg7Iqk7LyI66CI7YakXG4gICAgICAgICAgQXJyYXkuZnJvbSh7IGxlbmd0aDogNSB9KS5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInAtMyBiZy1zbGF0ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc2xhdGUtMTAwIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLXNsYXRlLTIwMCByb3VuZGVkIHctMTZcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmctc2xhdGUtMjAwIHJvdW5kZWRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgYmctc2xhdGUtMjAwIHJvdW5kZWQgdy0yMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLXNsYXRlLTIwMCByb3VuZGVkIHctMTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKVxuICAgICAgICApIDogKFxuICAgICAgICAgIGluZGljYXRvcnMubWFwKChpbmRpY2F0b3IsIGluZGV4KSA9PiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyBiZy1zbGF0ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc2xhdGUtMTAwIGhvdmVyOmJnLXNsYXRlLTEwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTcwMFwiPntpbmRpY2F0b3IubGFiZWx9PC9zcGFuPlxuICAgICAgICAgICAgICB7Z2V0VHJlbmRJY29uKGluZGljYXRvci50cmVuZCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMFwiPntpbmRpY2F0b3IudmFsdWV9PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3IoaW5kaWNhdG9yLnRyZW5kKX1gfT5cbiAgICAgICAgICAgICAgICB7aW5kaWNhdG9yLmNoYW5nZX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSlcbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog7ZG47YSwICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0zIGJvcmRlci10IGJvcmRlci1zbGF0ZS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtM1wiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDh2NGwzIDNtNi0zYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICB7bG9hZGluZyA/ICfsl4XrjbDsnbTtirgg7KSRLi4uJyA6XG4gICAgICAgICAgICAgbGFzdFVwZGF0ZSA/IGDrp4jsp4Drp4kg7JeF642w7J207Yq4OiAke2xhc3RVcGRhdGV9YCA6XG4gICAgICAgICAgICAgJzIw7LSI66eI64ukIOyXheuNsOydtO2KuCd9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU3BlZWRUcmFmZmljO1xuXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlNwZWVkVHJhZmZpYyIsInN5bWJvbCIsImluZGljYXRvcnMiLCJzZXRJbmRpY2F0b3JzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJsYXN0VXBkYXRlIiwic2V0TGFzdFVwZGF0ZSIsInRlY2huaWNhbExpZ2h0Iiwic2V0VGVjaG5pY2FsTGlnaHQiLCJpbmR1c3RyeUxpZ2h0Iiwic2V0SW5kdXN0cnlMaWdodCIsIm1hcmtldExpZ2h0Iiwic2V0TWFya2V0TGlnaHQiLCJyaXNrTGlnaHQiLCJzZXRSaXNrTGlnaHQiLCJuZXVyYWxMaWdodCIsInNldE5ldXJhbExpZ2h0IiwicGhhc2UxTG9hZGluZyIsInNldFBoYXNlMUxvYWRpbmciLCJwaGFzZTJMb2FkaW5nIiwic2V0UGhhc2UyTG9hZGluZyIsImxzdG1Mb2FkaW5nIiwic2V0THN0bUxvYWRpbmciLCJwcmVkaWN0aW9uRXJyb3IiLCJzZXRQcmVkaWN0aW9uRXJyb3IiLCJzaG93VGltZW91dE1lc3NhZ2UiLCJzZXRTaG93VGltZW91dE1lc3NhZ2UiLCJsYXN0UmVxdWVzdFRpbWUiLCJzZXRMYXN0UmVxdWVzdFRpbWUiLCJpbkZsaWdodCIsImZldGNoTWFya2V0RGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJmb3JtYXR0ZWREYXRhIiwibWFwIiwiaXRlbSIsImxhYmVsIiwidmFsdWUiLCJwcmljZSIsInRvTG9jYWxlU3RyaW5nIiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiY2hhbmdlIiwiY2hhbmdlUGVyY2VudCIsInRvRml4ZWQiLCJ0cmVuZCIsImNvbG9yIiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImVycm9yIiwiY29uc29sZSIsInJlc3VsdENvbG9yVG9TdGF0dXMiLCJ0b0xvd2VyQ2FzZSIsImdldENvbG9yRnJvbVJlc3VsdCIsIm9iaiIsInVuZGVmaW5lZCIsInJlc3VsdF9jb2xvciIsInRyYWZmaWNfbGlnaHQiLCJmZXRjaFN0YWdlZFByZWRpY3Rpb24iLCJjdXJyZW50Iiwibm93IiwibG9nIiwicGhhc2UxUmVzcG9uc2UiLCJtZXRob2QiLCJoZWFkZXJzIiwic2lnbmFsIiwiQWJvcnRTaWduYWwiLCJ0aW1lb3V0Iiwib2siLCJFcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJwaGFzZTFSZXN1bHQiLCJ0cmFmZmljX2xpZ2h0cyIsInRlY2huaWNhbCIsImluZHVzdHJ5IiwibWFya2V0IiwicmlzayIsInRpbWVvdXRUaW1lciIsInNldFRpbWVvdXQiLCJwaGFzZTJSZXNwb25zZSIsImNsZWFyVGltZW91dCIsInBoYXNlMlJlc3VsdCIsIm5ldXJhbCIsIm5hbWUiLCJtZXNzYWdlIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJnZXRUcmVuZEljb24iLCJzdmciLCJjbGFzc05hbWUiLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJnZXRDaGFuZ2VDb2xvciIsImdldFRyYWZmaWNMaWdodENvbG9yIiwiZ2V0U3RhdHVzVGV4dCIsImRpdiIsImgzIiwicCIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwibGVuZ3RoIiwiQXJyYXkiLCJmcm9tIiwiXyIsImluZGV4IiwiaW5kaWNhdG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SpeedTraffic.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5c621e197dd9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbmc3XFxEZXNrdG9wXFxob21lXFx1YnVudHVcXGZpbmFuY2lhbF9kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVjNjIxZTE5N2RkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: '사용자 맞춤 투자지원 AI',\n    description: '한양대학교 금융인공지능실무 - 사용자 맞춤형 투자지원 AI 시스템',\n    icons: {\n        icon: '/hanyang-logo.png',\n        shortcut: '/hanyang-logo.png',\n        apple: '/hanyang-logo.png'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ko\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\home\\ubuntu\\financial_dashboard\\src\\app\\page.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/fancy-canvas","vendor-chunks/lightweight-charts"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();