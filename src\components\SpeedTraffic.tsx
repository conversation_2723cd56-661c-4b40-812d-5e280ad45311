import React, { useState, useEffect, useRef } from 'react';

interface MarketIndicator {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  color: string;
}

interface SpeedTrafficProps {
  symbol?: string;
}

interface LSTMResult {
  symbol: string;
  train_until: string;
  predictions: Array<{
    date: string;
    pred_prob_up: number;
    predicted_label: number;
    actual_label: number;
    result_color: string;
  }>;
  summary_ko?: string;
}

interface MFIResult {
  symbol: string;
  date: string;
  mfi_14: number;
  traffic_light: string;
}

interface PredictionResult {
  lstm: LSTMResult;
  mfi: MFIResult;
}

const SpeedTraffic: React.FC<SpeedTrafficProps> = ({ symbol }) => {
  // Market indicators state (Pre-ticker mode)
  const [indicators, setIndicators] = useState<MarketIndicator[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Traffic lights state (Post-ticker mode)
  const [forecastLight, setForecastLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [technicalLight, setTechnicalLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [sentimentLight, setSentimentLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [volumeLight, setVolumeLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [riskLight, setRiskLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');

  // Prediction state
  const [predictionLoading, setPredictionLoading] = useState(false);
  const [predictionError, setPredictionError] = useState<string | null>(null);
  const [showTimeoutMessage, setShowTimeoutMessage] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState<number>(0);

  // Single-flight guard
  const inFlight = useRef(false);

  // 실제 시장 데이터 가져오기 (Pre-ticker mode)
  const fetchMarketData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/market_data');
      const result = await response.json();

      if (result.success && result.data) {
        const formattedData = result.data.map((item: any) => ({
          label: item.label,
          value: item.price.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          }),
          change: `${item.changePercent >= 0 ? '+' : ''}${item.changePercent.toFixed(2)}%`,
          trend: item.trend,
          color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'
        }));

        setIndicators(formattedData);
        setLastUpdate(new Date().toLocaleTimeString('ko-KR'));
      }
    } catch (error) {
      console.error('Failed to fetch market data:', error);
      // 에러 시 fallback 데이터 사용
      setIndicators([
        { label: 'S&P 500', value: '4,567.89', change: '+1.2%', trend: 'up', color: 'green' },
        { label: '나스닥', value: '14,234.56', change: '+0.8%', trend: 'up', color: 'green' },
        { label: '다우존스', value: '34,567.12', change: '-0.3%', trend: 'down', color: 'red' },
        { label: 'VIX', value: '18.45', change: '-2.1%', trend: 'down', color: 'yellow' },
        { label: '달러/원', value: '1,327.50', change: '+0.5%', trend: 'up', color: 'green' },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Convert result color to traffic light status
  const resultColorToStatus = (color: string): 'good' | 'warning' | 'danger' => {
    switch (color?.toLowerCase()) {
      case 'green':
      case 'good':
        return 'good';
      case 'yellow':
      case 'warning':
        return 'warning';
      case 'red':
      case 'danger':
        return 'danger';
      default:
        return 'warning';
    }
  };

  // Extract color from LSTM/MFI results
  const getColorFromResult = (obj: any): string | undefined => {
    if (!obj) return undefined;
    if (typeof obj === 'string') return obj;
    if (obj.result_color) return obj.result_color;
    if (obj.traffic_light) return obj.traffic_light;
    if (obj.color) return obj.color;
    return undefined;
  };

  // Simplified LSTM/MFI prediction using direct HTTP request
  const fetchPrediction = async () => {
    if (!symbol || inFlight.current) return;

    // Prevent too frequent requests (minimum 10 seconds between requests)
    const now = Date.now();
    if (now - lastRequestTime < 10000) {
      console.log('Prediction request throttled - too frequent');
      return;
    }

    try {
      // Set single-flight guard
      inFlight.current = true;
      setLastRequestTime(now);

      // Reset lights 1 and 2 to grey/inactive state, keep 3-5 permanently inactive
      setForecastLight('inactive');
      setTechnicalLight('inactive');
      // Lights 3, 4, and 5 remain permanently inactive
      setSentimentLight('inactive');
      setVolumeLight('inactive');
      setRiskLight('inactive');

      setPredictionLoading(true);
      setPredictionError(null);
      setShowTimeoutMessage(false);

      console.log(`[SpeedTraffic] Starting prediction for ${symbol}`);

      // Start 20-second timer for Korean timeout message
      const timeoutTimer = setTimeout(() => {
        setShowTimeoutMessage(true);
      }, 20000);

      // Make direct HTTP request to simplified LSTM prediction API
      const response = await fetch(`/api/lstm_prediction_simple?symbol=${symbol}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(60000), // 60 second timeout
      });

      clearTimeout(timeoutTimer);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: PredictionResult = await response.json();
      console.log(`[SpeedTraffic] Prediction result:`, result);

      // Process LSTM results
      if (result.lstm) {
        let lstmColor: string | undefined;
        if (Array.isArray(result.lstm.predictions) && result.lstm.predictions.length > 0) {
          lstmColor = getColorFromResult(result.lstm.predictions[0]);
        }
        if (!lstmColor) {
          lstmColor = getColorFromResult(result.lstm);
        }

        if (lstmColor) {
          setForecastLight(resultColorToStatus(lstmColor));
          console.log(`[SpeedTraffic] LSTM forecast light set to: ${resultColorToStatus(lstmColor)}`);
        }
      }

      // Process MFI results
      if (result.mfi) {
        const mfiColor = getColorFromResult(result.mfi);
        if (mfiColor) {
          setTechnicalLight(resultColorToStatus(mfiColor));
          console.log(`[SpeedTraffic] MFI technical light set to: ${resultColorToStatus(mfiColor)}`);
        }
      }

      // Keep lights 3, 4, and 5 permanently inactive as per requirements
      setSentimentLight('inactive'); // Permanently inactive
      setVolumeLight('inactive'); // Permanently inactive
      setRiskLight('inactive'); // Permanently inactive

      console.log(`[SpeedTraffic] Prediction completed successfully for ${symbol}`);

    } catch (error) {
      console.error('Prediction error:', error);

      if (error instanceof Error) {
        if (error.name === 'TimeoutError') {
          setPredictionError('요청 시간 초과 (60초)');
        } else {
          setPredictionError(`예측 실패: ${error.message}`);
        }
      } else {
        setPredictionError('예측 서비스 연결 실패');
      }

      // Reset lights 1 and 2 to inactive on error, keep 3-5 permanently inactive
      setForecastLight('inactive');
      setTechnicalLight('inactive');
      // Lights 3, 4, and 5 remain permanently inactive
      setSentimentLight('inactive');
      setVolumeLight('inactive');
      setRiskLight('inactive');
    } finally {
      setPredictionLoading(false);
      setShowTimeoutMessage(false);
      inFlight.current = false;
    }
  };

  // Effects
  useEffect(() => {
    if (symbol) {
      // When symbol is provided, fetch prediction once
      fetchPrediction();
    } else {
      // When no symbol, fetch market data initially
      fetchMarketData();
    }
  }, [symbol]);

  // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)
  useEffect(() => {
    if (!symbol) {
      const interval = setInterval(() => {
        fetchMarketData();
      }, 20000);

      return () => clearInterval(interval);
    }
  }, [symbol]);

  // Utility functions for rendering
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
          </svg>
        );
      case 'down':
        return (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        );
    }
  };

  const getChangeColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-slate-500';
    }
  };

  const getTrafficLightColor = (status: 'good' | 'warning' | 'danger') => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'danger': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = (status: 'good' | 'warning' | 'danger') => {
    switch (status) {
      case 'good': return '양호';
      case 'warning': return '보통';
      case 'danger': return '주의';
      default: return '분석중';
    }
  };

  // Post-ticker mode: Traffic lights display
  if (symbol) {
    return (
      <div className="space-y-4 max-w-full overflow-hidden">
        {/* 헤더 */}
        <div className="text-center">
          <h3 className="font-semibold text-slate-900 mb-1">SpeedTraffic™</h3>
          <p className="text-sm text-slate-600">{symbol} 투자 적격성</p>
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse mx-auto mt-2"></div>
        </div>

        {/* 5-Light 신호등 시스템 */}
        <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-xl border border-slate-700">
          <div className="space-y-4">
            {/* Forecast Light (LSTM) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      forecastLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(forecastLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      forecastLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {forecastLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(forecastLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    forecastLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Forecast
                    <span className="ml-2 text-xs text-blue-400 font-normal">
                      LSTM
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  forecastLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  forecastLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  forecastLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {forecastLight === 'inactive' ? '대기중' : getStatusText(forecastLight)}
                </span>
              </div>
            </div>

            {/* Technical Light (MFI) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      technicalLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {technicalLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(technicalLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Technical
                    <span className="ml-2 text-xs text-orange-400 font-normal">
                      MFI-14
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  technicalLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  technicalLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  technicalLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)}
                </span>
              </div>
            </div>

            {/* Sentiment Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      sentimentLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(sentimentLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      sentimentLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {sentimentLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(sentimentLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    sentimentLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Sentiment
                    <span className="ml-2 text-xs text-purple-400 font-normal">
                      뉴스분석
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  sentimentLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  sentimentLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  sentimentLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {sentimentLight === 'inactive' ? '대기중' : getStatusText(sentimentLight)}
                </span>
              </div>
            </div>

            {/* Volume Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      volumeLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(volumeLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      volumeLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {volumeLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(volumeLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    volumeLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Volume
                    <span className="ml-2 text-xs text-cyan-400 font-normal">
                      거래량
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  volumeLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  volumeLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  volumeLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {volumeLight === 'inactive' ? '대기중' : getStatusText(volumeLight)}
                </span>
              </div>
            </div>

            {/* Risk Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      riskLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {riskLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(riskLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Risk
                    <span className="ml-2 text-xs text-red-400 font-normal">
                      위험도
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  riskLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  riskLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  riskLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {predictionLoading && (
          <div className="bg-slate-100 rounded-lg p-4">
            <div className="flex items-center justify-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
              <span className="text-sm text-slate-600">
                LSTM 및 MFI 분석 중...
              </span>
            </div>
          </div>
        )}

        {/* Korean Timeout Message */}
        {showTimeoutMessage && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
            <div className="text-xs text-yellow-600 text-center">
              이 작업은 시간이 걸립니다... (최대 60초)
            </div>
          </div>
        )}

        {/* Error Display */}
        {predictionError && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-xs text-red-600">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span>오류: {predictionError}</span>
            </div>
          </div>
        )}

        {/* Retry Button */}
        {predictionError && !predictionLoading && (
          <div className="text-center">
            <button
              onClick={fetchPrediction}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors"
            >
              다시 시도
            </button>
          </div>
        )}

        {/* Status Information */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
            <svg className={`w-3 h-3 ${predictionLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>
              {predictionLoading ? 'Intel 최적화 5-Light 분석 중...' : 'Intel 최적화 5-Light 종합 분석 시스템'}
            </span>
          </div>
          <div className="mt-1 text-xs text-green-400">
            예측 | 기술적 | 감정 | 거래량 | 위험도 - Intel oneDNN 가속
          </div>
        </div>
      </div>
    );
  }

  // Pre-ticker mode: Market indicators display
  return (
    <div className="space-y-4">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-slate-900">시장 현황</h3>
        <div className={`w-2 h-2 rounded-full animate-pulse ${loading ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
      </div>

      {/* 지표 목록 */}
      <div className="space-y-3">
        {loading && indicators.length === 0 ? (
          // 로딩 스켈레톤
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse">
              <div className="flex items-center justify-between mb-1">
                <div className="h-4 bg-slate-200 rounded w-16"></div>
                <div className="w-4 h-4 bg-slate-200 rounded"></div>
              </div>
              <div className="flex items-center justify-between">
                <div className="h-6 bg-slate-200 rounded w-20"></div>
                <div className="h-4 bg-slate-200 rounded w-12"></div>
              </div>
            </div>
          ))
        ) : (
          indicators.map((indicator, index) => (
          <div
            key={index}
            className="p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200"
          >
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-slate-700">{indicator.label}</span>
              {getTrendIcon(indicator.trend)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold text-slate-900">{indicator.value}</span>
              <span className={`text-sm font-medium ${getChangeColor(indicator.trend)}`}>
                {indicator.change}
              </span>
            </div>
          </div>
          ))
        )}
      </div>

      {/* 푸터 */}
      <div className="pt-3 border-t border-slate-200">
        <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>
            {loading ? '업데이트 중...' :
             lastUpdate ? `마지막 업데이트: ${lastUpdate}` :
             '20초마다 업데이트'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SpeedTraffic;

